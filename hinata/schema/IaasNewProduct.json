{"type": "object", "required": ["instance_campaign_zones_badge", "instance_campaign_preferential_tag"], "properties": {"instance_campaign_zones_badge": {"type": "object", "description": "新品推荐中的badge 文案设置", "properties": {"zones": {"type": "array", "description": "新品推荐的可用zones设置", "items": {"type": "string", "description": "zone"}}, "zh-cn": {"type": "string", "description": "badge 文案中文描述"}, "en": {"type": "string", "description": "badge 文案英文描述"}}}, "instance_campaign_preferential_tag": {"type": "array", "description": "产品推荐列表tag样式设置，可以配置多个tag标签，可根据instance_class/instance_type_id/instance_style配", "items": {"type": "object", "description": "主机列表中标签样式", "properties": {"tag_style": {"type": "string", "description": "标签背景颜色", "enum": ["ib", "new", "rdma"]}, "tag_text": {"type": "object", "description": "tag文案", "properties": {"zh-cn": {"type": "string", "description": "tag文案中文描述"}, "en": {"type": "string", "description": "tag文案英文描述"}}}, "enabled_type": {"type": "array", "description": "设置可展示的类型属性，instance_class/instance_type_id/instance_style", "items": {"type": "string", "description": "instance_class/instance_type_id/instance_style"}}, "disabled_type": {"type": "array", "description": "设置禁用的类型属性，可特殊处理 过滤 enabled_type 中的某些 instance_class/instance_type_id/instance_style", "items": {"type": "string", "description": "instance_class/instance_type_id/instance_style"}}}}}}}