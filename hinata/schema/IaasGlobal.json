{"type": "object", "required": ["filter_config"], "properties": {"filter_config": {"type": "array", "description": "iaas 资源前端显示过滤方案配置", "items": {"type": "object", "required": ["type", "formula"], "properties": {"type": {"type": "string", "description": "过滤的资源类型 instances | volumes | instance_styles", "enum": ["instances", "volumes", "instance_styles", "zones"]}, "formula": {"type": "array", "uniqueItems": true, "description": "实施过滤的方案", "items": {"type": "object", "required": ["factor", "target"], "properties": {"factor": {"type": "object", "description": "参与过滤的条件", "required": ["key", "RegExp"], "properties": {"key": {"description": "字段（如 user.user_id）", "type": "string"}, "RegExp": {"description": "命中字段的正则表达 （new RegExp 的参数）", "type": "string"}}}, "target": {"type": "object", "description": "被过滤的目标", "required": ["values"], "properties": {"key": {"description": "需要命中的目标字段（目标已经是字符串则不填）", "type": "string"}, "values": {"description": "命中的值", "type": "array", "items": {"type": "string"}}, "not": {"description": "取非（相反的值）", "type": "boolean"}}}}}}}}}, "default_monthly": {"type": "boolean", "description": "是否默认包年包月"}, "support_all_resource_standards": {"description": "是否支持查询全部资源存储池类型", "type": "boolean"}}}