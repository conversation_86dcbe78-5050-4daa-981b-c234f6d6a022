{"type": "object", "required": ["cpu_filters", "memory_filters", "default_login_type", "os_disk_size", "max_count", "support_repl_count_classes", "max_volume_count", "max_nic_count", "show_instance_bandwidth"], "properties": {"max_count": {"description": "创建主机支持的最大数量", "type": "number"}, "cpu_filters": {"description": "cpu 可检索值", "type": "array", "uniqueItems": true, "items": {"type": "number"}}, "memory_filters": {"description": "memory 可检索值", "type": "array", "uniqueItems": true, "items": {"type": "number"}}, "default_login_type": {"description": "默认登陆方式", "type": "string", "enum": ["passwd", "keypair"]}, "os_disk_size": {"description": "系统盘大小", "type": "object", "required": ["windows", "linux", "unix"], "properties": {"windows": {"type": "object", "required": ["min", "max", "default"], "properties": {"min": {"description": "如果配置的最小值小于所选镜像支持的最小值，则实际最小值取镜像最小值", "type": "number"}, "max": {"description": "最大值", "type": "number"}, "default": {"description": "如果默认值小于实际最小值，则实际取最小值", "type": "number"}, "step": {"description": "每次增加或者减少的数量", "type": "number"}}}, "linux": {"type": "object", "required": ["min", "max", "default"], "properties": {"min": {"description": "如果配置的最小值小于所选镜像支持的最小值，则实际最小值取镜像最小值", "type": "number"}, "max": {"description": "最大值", "type": "number"}, "default": {"description": "如果默认值小于实际最小值，则实际取最小值", "type": "number"}, "step": {"description": "每次增加或者减少的数量", "type": "number"}}}, "unix": {"type": "object", "required": ["min", "max", "default"], "properties": {"min": {"description": "如果配置的最小值小于所选镜像支持的最小值，则实际最小值取镜像最小值", "type": "number"}, "max": {"description": "最大值", "type": "number"}, "default": {"description": "如果默认值小于实际最小值，则实际取最小值", "type": "number"}, "step": {"description": "每次增加或者减少的数量", "type": "number"}}}}}, "unsupport_bm1_os_disk": {"description": "是否关闭bm1设置系统盘", "type": "boolean"}, "tag_background": {"description": "规格标签背景颜色", "type": "array", "uniqueItems": true, "items": {"type": "object", "properties": {"tag": {"description": "标签值", "type": "string"}, "background": {"description": "背景色值", "type": "string"}}}}, "show_instance_bandwidth": {"type": "boolean", "description": "是否显示规格 bandwidth 列"}, "support_repl_count_classes": {"type": "array", "description": "主机支持的副本数量", "items": {"type": "object", "properties": {"instance_class": {"type": "number", "description": "主机规格"}, "repl_counts": {"type": "array", "description": "副本数量", "items": {"type": "number"}}, "default_repl_count": {"type": "number", "description": "默认的副本数量"}}}}, "max_volume_count": {"type": "number", "description": "主机挂载最大的硬盘数量"}, "max_nic_count": {"type": "number", "description": "主机挂载最大的网卡数量"}, "default_eip_bandwidth": {"type": "number", "description": "eip默认带宽"}, "default_eip_group": {"type": "object", "description": "默认eip组", "properties": {"default": {"type": "string"}, "zones": {"type": "array", "items": {"type": "object", "properties": {"zone": {"type": "string"}, "eip_group": {"type": "string"}}}}}}, "support_cpu_topology": {"type": "boolean", "description": "是否支持调整CPU拓扑结构"}, "default_cpu_topology_rule": {"type": "string", "description": "初始的 cpu 拓扑结构规则", "enum": ["cpu * 1 * 1", "1 * cpu * 1", "1 * 1 * cpu"]}, "support_cpu_model": {"type": "boolean", "description": "是否支持设置CPU体系架构"}, "support_instance_expiration": {"type": "boolean", "description": "是否支持实例过期时间"}, "unusable_instance_class_conversion": {"type": "object", "description": "无法使用的主机类型转换逻辑", "additionalProperties": {"type": "object", "properties": {"additionalProperties": {"type": ["string", "number"]}}}}}}