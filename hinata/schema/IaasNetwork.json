{"type": "object", "required": ["support_optional_vxnet0", "optional_vxnet0_filters", "vpc_eip_associate_mode", "bm1_net_trunk_mode", "default_eip_group"], "properties": {"vpc_type_default": {"type": "array", "description": "可指定不同区域，不同vpc类型的默认值", "uniqueItems": true, "items": {"type": "object", "required": ["label", "value"], "properties": {"label": {"type": "string"}, "value": {"type": "number"}}}}, "support_optional_vxnet0": {"type": "boolean", "description": "是否支持自选基础网络"}, "default_eip_group": {"type": "string", "description": "创建主机流程中，创建eip的默认ip组"}, "optional_vxnet0_filters": {"type": "array", "description": "自选基础网络过滤（依据visibility字段的值进行过滤，配置的参数将会被过滤掉不会展示）", "uniqueItems": true, "items": {"type": "string"}}, "vpc_eip_associate_mode": {"type": "array", "description": "vpc创建时，DescribeEip接口的参数associate_mode", "uniqueItems": true, "items": {"type": "number"}}, "bm1_net_trunk_mode": {"description": "bm1支持网络模式", "type": "string", "enum": ["vxlan_trunk_mode", "vlan_trunk_mode"]}, "vpc_user_exclusive_ipv4": {"type": "array", "description": "vpc创建，用户自定义的ipv4范围", "uniqueItems": true, "items": {"type": "object", "required": ["user_id", "ip"], "properties": {"user_id": {"type": "string"}, "ip": {"type": "array", "uniqueItems": true, "items": {"type": "string"}}}}}, "support_bm_vxnet_zones": {"description": "vpc创建时是否支持创建BM私有网络的zone", "type": "array", "uniqueItems": true, "items": {"type": "string"}}, "no_support_routing_table": {"description": "vpc创建是否不支持路由表", "type": "boolean"}, "show_nat_version_upgrade_announcement": {"description": "nat列表，是否显示升级提示", "type": "boolean"}, "nat_monitor_step": {"description": "nat详情监控，监控频率选项", "type": "array", "uniqueItems": true, "items": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"type": "string"}}}}, "nat_monitor_range": {"description": "nat详情监控，监控时间范围", "type": "array", "uniqueItems": true, "items": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"type": "number"}}}}, "eip_establish_mode": {"description": "vpc创建，eip绑定的方式选项", "type": "array", "uniqueItems": true, "items": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"type": "number"}}}}}}