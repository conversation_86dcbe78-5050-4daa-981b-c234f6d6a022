#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os,sys,site
__dirname = sys.path[0]
if os.path.isdir(os.path.join('/pitrix/lib/pitrix-hinata-admin-cli')):
    site.addsitedir('/pitrix/lib/pitrix-hinata-admin-cli')
import admincli

'''
本脚本用于全新安装、升级时，本工程当前版本运行时所需的原厂配置, 由Installer自动执行。

脚本执行环境：
1) 配置中心组件启动完成
2) WS节点已安装admin-cli组件
3) 任意一WS节点执行一次即可(多次执行也无副作用)
'''
cli = admincli.connect("http://api.qingcloud.com:13000/")
cli.login('dev', 'dev@k6xvodNJ')

def cat(*p):
    file_path = os.path.join(__dirname, *p)
    with open(file_path, 'r') as stream:
        text = stream.read()
    return text

# 设置应用(不存在则创建)
cli.set_app('webconsole-iaas')
# 设置模型(不存在则创建)
cli.set_schema('IaasInstance', { 'json_schema': cat('schema/IaasInstance.json'), 'desc': 'Portal Iaas 主机配置模型' })
cli.set_schema('IaasVolume',   { 'json_schema': cat('schema/IaasVolume.json'), 'desc': 'Portal Iaas 硬盘配置模型' })
cli.set_schema('IaasNetwork',  { 'json_schema': cat('schema/IaasNetwork.json'), 'desc': 'Portal Iaas 网络配置模型' })
cli.set_schema('IaasImage',    { 'json_schema': cat('schema/IaasImage.json'), 'desc': 'Portal Iaas 镜像配置模型' })
cli.set_schema('IaasGlobal',   { 'json_schema': cat('schema/IaasGlobal.json'), 'desc': 'Portal Iaas 主机全局配置模型' })
# 设置模型原厂默认值
cli.set_schema_preset(('IaasInstance', 'ORIGINAL'), { "content": cat('configs/iaas-instance.yaml'), "content_type": "yaml", "desc": "原厂默认配置" })
cli.set_schema_preset(('IaasVolume'  , 'ORIGINAL'), { "content": cat('configs/iaas-volume.yaml'), "content_type": "yaml", "desc": "原厂默认配置" })
cli.set_schema_preset(('IaasNetwork' , 'ORIGINAL'), { "content": cat('configs/iaas-network.yaml'), "content_type": "yaml", "desc": "原厂默认配置" })
cli.set_schema_preset(('IaasImage'   , 'ORIGINAL'), { "content": cat('configs/iaas-image.yaml'), "content_type": "yaml", "desc": "原厂默认配置" })
cli.set_schema_preset(('IaasGlobal'  , 'ORIGINAL'), { "content": cat('configs/iaas-global.yaml'), "content_type": "yaml", "desc": "原厂默认配置" })
# 设置配置(不存在则创建)
cli.set_meta(('webconsole-iaas', 'iaas-instance'),  { "schema_name": 'IaasInstance', "content_type": "yaml", "desc": "IAAS 主机配置" })
cli.set_meta(('webconsole-iaas', 'iaas-volume'),    { "schema_name": 'IaasVolume', "content_type": "yaml", "desc": "IAAS 数据盘配置" })
cli.set_meta(('webconsole-iaas', 'iaas-network'),   { "schema_name": 'IaasNetwork', "content_type": "yaml", "desc": "IAAS 网络配置" })
cli.set_meta(('webconsole-iaas', 'iaas-image'),     { "schema_name": 'IaasImage', "content_type": "yaml", "desc": "IAAS 镜像配置" })
cli.set_meta(('webconsole-iaas', 'iaas-global'),    { "schema_name": 'IaasGlobal', "content_type": "yaml", "desc": "IAAS 通用配置" })

print('[webconsole-iaas] import complete')

