cpu_filters:
  - 1
  - 2
  - 4
  - 8
  - 12
  - 16
  - 24
  - 32
  - 64
  - 96
  - 128
memory_filters:
  - 1024
  - 2048
  - 4096
  - 8192
  - 12288
  - 16384
  - 24576
  - 32768
  - 49152
  - 65536
  - 98304
  - 131072
  - 196608
  - 262144
  - 393216
  - 524288
os_disk_size:
  windows:
    min: 50
    max: 300
    default: 50
    step: 10
  linux:
    min: 20
    max: 300
    default: 50
    step: 10
  unix:
    min: 20
    max: 300
    default: 50
    step: 10
default_login_type: passwd
max_count: 10
unsupport_bm1_os_disk: true
show_instance_bandwidth: false
tag_background:
  - tag: new
    background: 'linear-gradient(65.59deg, #0073FA 10.59%, #67E3FE 131.35%)'
  - tag: rdma
    background: 'linear-gradient(65.59deg, #FA6400 10.59%, #FEA267 131.35%)'
  - tag: ib
    background: 'linear-gradient(228.94deg, #5DCA7C 30.26%, #4DAA77 84.87%)'
support_repl_count_classes:
  - instance_class: 6
    repl_counts: [1, 2, 3]
    default_repl_count: 3
  - instance_class: 7
    repl_counts: [1, 2, 3]
    default_repl_count: 2
max_volume_count: 10
max_nic_count: 1
default_eip_bandwidth: 4
default_eip_group:
  default: eipg-00000000
  zones:
    - zone: ap2a
      eip_group: eipg-00000001
support_cpu_topology: false
support_cpu_model: false
default_cpu_topology_rule: '1 * cpu * 1'
support_instance_style_by_server: true
support_instance_expiration: false
unusable_instance_class_conversion:
  203_203:
    0: [1]
    204_204:
      0: [1, 9]
      1: [9]
    203_204:
      0: [1, 9]
      1: [9]
    204_203:
      0: [1]
