{"name": "@QCFE/pitrix-webconsole-iaas", "version": "0.0.1-plg.5", "description": "project pitrix webconsole iaas", "keywords": ["webconsole", "iaas"], "license": "UNLICENSED", "author": "QingCloud F2E Group", "main": "lib/exports/index.js", "types": "lib/exports/index.d.ts", "files": ["lib"], "scripts": {"build": "rm -rf dist; npx webpack build --config ./webpack.config.js", "build:npm": "rm -rf ./lib/*; tsc --project ./src/exports", "cspell": "cspell '**/*.{ts,tsx,md}'", "deploy-staging": "sh ./bin/subportal_deploy.sh console_subportal staging", "deploy-testing": "sh ./bin/subportal_deploy.sh console_subportal new-testing", "dev": "NODE_ENV=development npx webpack serve --config ./dev.webpack.config.js", "format": "prettier --write .", "lint": "eslint --ext .tsx,.ts src", "prettier:check": "prettier --check .", "pretty-quick": "pretty-quick --staged", "watch": "rm -rf dist; MODE=development NODE_ENV=development NO_HASH=true npx webpack build --config ./webpack.config.js --watch"}, "resolutions": {"@types/react": "17.0.2"}, "dependencies": {"@ebay/nice-modal-react": "^1.2.9", "@emotion/react": "11.7.1", "@emotion/styled": "11.6.0", "@floating-ui/react": "^0.26.4", "@hookform/error-message": "^2.0.1", "@pitrix/portal-formly": "^1.3.3", "@pitrix/portal-ui": "^2.1.0", "@pitrix/portal-widget": "^2.1.0", "axios": "^1.3.4", "bignumber.js": "^9.1.1", "classnames": "^2.3.2", "dayjs": "^1.11.13", "echarts": "^5.5.0", "immer": "^9.0.19", "jotai": "^2.9.1", "js-base64": "^3.7.5", "js-cookie": "^3.0.1", "lodash-es": "^4.17.21", "mobx": "^6.8.0", "mobx-react": "^7.6.0", "query-string": "^8.1.0", "react": "17.0.2", "react-beautiful-dnd": "^13.1.1", "react-collapse": "^5.1.1", "react-color": "^2.19.3", "react-contexify": "^6.0.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "17.0.2", "react-hook-form": "7.43.5", "react-query": "^3.39.3", "react-router-dom": "^6.8.2", "react-use": "^17.4.0", "rxjs": "^7.8.0"}, "devDependencies": {"@QCFE/hinata": "^1.23.0", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@tanstack/react-table": "^8.21.2", "@types/js-cookie": "^3.0.3", "@types/lodash-es": "^4.17.7", "@types/react": "17.0.2", "@types/react-beautiful-dnd": "^13.1.7", "@types/react-collapse": "^5.0.1", "@types/react-color": "^3.0.6", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "17.0.2", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.9", "autoprefixer": "^9.8.2", "circular-dependency-plugin": "^5.2.2", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "cspell": "^5.4.1", "css-loader": "^3.6.0", "css-minimizer-webpack-plugin": "^4.0.1", "css-mqpacker": "^7.0.0", "cssnano": "^4.1.10", "esbuild-loader": "^3.0.1", "eslint": "^8.42.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-import-resolver-typescript": "^3.8.7", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^0.9.0", "pinst": "^3.0.0", "postcss": "^8.5.2", "postcss-flexbugs-fixes": "^4.2.1", "postcss-import": "^12.0.1", "postcss-loader": "^3.0.0", "postcss-nested": "^4.2.1", "postcss-preset-env": "^6.7.0", "postcss-scss": "^2.1.1", "prettier": "^2.8.8", "pretty-quick": "^3.1.3", "sass": "1.32.8", "sass-loader": "8.0.2", "style-loader": "^1.2.1", "thread-loader": "^4.0.4", "typescript": "4.9.5", "webpack": "^5.75.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.9.0", "webpack-merge": "^5.8.0", "webpackbar": "^5.0.2"}, "peerDependencies": {"@pitrix/hinata": ">=1.23.0", "@pitrix/portal-widget": ">=1.2.1"}}