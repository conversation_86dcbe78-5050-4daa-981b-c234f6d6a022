import { Modal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';

import apiRequest from 'utils/request';
import type { Instance } from 'api/types';

export function openSyncTimeAction(instance: Instance, onRefresh?: () => void) {
  Modal.info({
    id: '#micro-iaas-root',
    title: t('确定要开启平台对云服务器的时间同步吗？', 'Are you sure you want to enable platform synchronization of the cloud server time?'),
    onOk: async () => {
      await apiRequest({
        params: {
          action: 'ModifyInstanceAttributes',
          instances: instance?.instance_id,
          features: 4,
        },
      });
      onRefresh?.();
    },
  });
}

export function closeSyncTimeAction(instance: Instance, onRefresh?: () => void) {
  Modal.info({
    id: '#micro-iaas-root',
    title: t('确定要关闭平台对云服务器的时间同步吗？', 'Are you sure you want to disable platform synchronization of the cloud server time?'),
    onOk: async () => {
      await apiRequest({
        params: {
          action: 'ModifyInstanceAttributes',
          instances: instance?.instance_id,
          features: 5,
        },
      });
      onRefresh?.();
    },
  });
}
