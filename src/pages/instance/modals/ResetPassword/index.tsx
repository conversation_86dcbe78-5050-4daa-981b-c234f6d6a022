import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Input, Alert, Modal as UiModal, portal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';

import type { Instance } from 'api/types';

import { Modal, FormField } from 'components';
import apiRequest from 'utils/request';

interface Props {
  instance: Instance;
}

interface FormData {
  login_passwd: string;
}

function ResetPassword({ instance }: Props) {
  const {
    formState: { errors },
  } = useFormContext();
  return (
    <div>
      <Alert
        css={{ marginBottom: 16 }}
        closable={false}
        type="info"
        showIcon
        description={t(
          '密码修改后需进行“重启云服务器”使新密码生效。',
          'After the password is modified, the cloud server needs to be "restarted" for the new password to take effect.',
        )}
      />
      <FormField
        labelWidth="72px"
        title={t('云服务器', 'Instance')}
        displayType="vertical"
        data={[{ resource_id: instance.instance_id, resource_name: instance.instance_name }]}
        titleLineHeight="20px"
      />
      <FormField title={t('用户名', 'Username')} labelWidth="72px">
        <Input width={328} readOnly defaultValue={instance?.image?.default_user ?? ''} />
      </FormField>
      <Controller
        name="login_passwd"
        rules={{
          required: true,
          validate: (v) => {
            return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!#$%&*.@^_~])[\w!#$%&*.@^~]{8,}$/.test(v);
          },
        }}
        render={({ field }) => (
          <FormField title={t('密码', 'Password')} labelWidth="72px">
            <Input width={328} type="password" {...field} />
            <portal.div
              css={{
                fontSize: 's',
                marginTop: '4px',
                color: errors.login_passwd ? 'error.default' : 'neutral.8',
              }}
            >
              {t(
                '密码必须包含数字、大小写字母和符号 ~!@#$%^&*_. 长度至少为 8 位。',
                'Password must contain numbers, uppercase and lowercase letters, and symbols ~!@#$%^&*_. It must be at least 8 characters long.',
              )}
            </portal.div>
          </FormField>
        )}
      />
    </div>
  );
}

export const resetPasswordAction = async (instance: Instance, onRefresh?: () => void) => {
  // const { f_resetpwd, features, root_partition_fs, agent_type } = instance?.image ?? {};
  // const onlySupportRunningStatus = (features & 4) === 4 || root_partition_fs === 'lvm';

  const getErrorText = () => {
    const {
      status,
      image: { f_resetpwd, features, root_partition_fs, agent_type },
    } = instance;

    /** 是否仅支持开机重置密码 */
    const onlySupportRunningStatus = (features & 4) === 4 || root_partition_fs === 'lvm';

    const notSupport = t('当前主机的镜像不支持重置密码！', 'The current host image does not support password reset!');

    if (!f_resetpwd) {
      return notSupport;
    }

    if (status === 'stopped') {
      if (onlySupportRunningStatus) {
        return t(
          '当前主机的镜像仅支持开机重置密码，请开机后再操作！',
          'The image of the current host only supports password reset upon power-on. Please do so after power-on!',
        );
      }

      return '';
    }

    if (status === 'running') {
      /** 仅支持开机重置密码 且配置了支持开机重置密码 */
      if (window?.CONFIG?.support_reset_login_passwd_online && onlySupportRunningStatus) {
        if (agent_type === 'pitrix') {
          return '';
        }

        return notSupport;
      }

      return t(
        '当前主机的镜像仅支持关机重置密码，请关机后再操作！',
        'The image of the current host only supports password reset while the host is powered off. Please shut down before proceeding.',
      );
    }

    return notSupport;
  };

  if (getErrorText()) {
    UiModal.alert({
      id: '#micro-iaas-root',
      title: t('操作错误', 'Operation error'),
      content: getErrorText(),
    });
    return;
  }

  Modal.form<FormData>({
    title: t('重置登陆密码', 'Reset Login password'),
    width: 600,
    children: <ResetPassword instance={instance} />,
    onAsyncOk: async (data) => {
      apiRequest({
        params: {
          action: 'ResetLoginPasswd',
          zone: window.user.zone,
          owner: window.user.user_id,
          instances: [instance.instance_id],
          login_passwd: data.login_passwd,
        },
      });
      onRefresh?.();
    },
  });
};

export default ResetPassword;
