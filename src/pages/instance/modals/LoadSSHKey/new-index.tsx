import React, { useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Table, Checkbox, portal } from '@pitrix/portal-ui';
import { requestDescribeKeyPairs, FormField, t } from '@pitrix/portal-widget';
import type { Keypair } from '@pitrix/portal-widget';

import Modal from 'components/Modal';
import { Instance } from 'api/types';
import { ModalTable } from 'common';
import { apiRequest } from 'utils';

interface FormData {
  keypairs: React.Key[];
  disable_ssh_passwd: boolean;
}

interface Props {
  instance: Instance;
}

// 表单内容组件
function LoadSSHKeyForm({ instance }: Props): JSX.Element {
  const { control, formState: { errors }, watch } = useFormContext<FormData>();
  const [newSelected, setNewSelected] = useState<React.Key[]>([]);

  const zone = window.user.zone;
  const owner = window.user.user_id;
  const project_id = window.global_info.project_id;

  const keypairDatas = ((instance?.keypair_ids as unknown as string[]) || [])?.map((id) => ({
    resource_id: id,
    resource_name: id,
    link: `/${zone}/${id}/`,
  }));

  const columns = [
    {
      title: '名称',
      dataIndex: 'keypair_name',
      key: 'keypair_name',
    },
    {
      title: '加密方式',
      key: 'encrypt_method',
      dataIndex: 'encrypt_method',
    },
  ];

  const fetchData = <P,>(params: P) =>
    requestDescribeKeyPairs({
      zone,
      owner,
      offset: 0,
      limit: 10,
      ...params,
      project_id,
    });

  return (
    <>
      <FormField
        title={t('云服务器', 'Instance')}
        displayType="vertical"
        data={[{ resource_id: instance.instance_id, resource_name: instance.instance_name }]}
        titleLineHeight="20px"
      />

      <FormField
        title={t('已加载 SSH 秘钥', 'Loaded SSH keys')}
        displayType="horizontal"
        data={keypairDatas}
        titleLineHeight="20px"
      />

      <Controller
        name="disable_ssh_passwd"
        control={control}
        render={({ field }) => (
          <FormField title={t('禁用SSH密码', 'Disable SSH Password')}>
            <Checkbox
              checked={field.value}
              onChange={field.onChange}
            >
              {t('禁用SSH密码登录', 'Disable SSH password login')}
            </Checkbox>
          </FormField>
        )}
      />

      <Controller
        name="keypairs"
        control={control}
        rules={{
          validate: (value) => {
            if (!value || value.length === 0) {
              return t('请选择至少一个SSH密钥', 'Please select at least one SSH key');
            }
            return true;
          },
        }}
        render={({ field }) => (
          <FormField title={t('选择SSH密钥', 'Select SSH Keys')}>
            <ModalTable<Keypair>
              placeholder="输入SSH密钥名称进行搜索"
              columns={columns}
              rowKey="keypair_id"
              fetchData={fetchData}
              resultKey="keypair_set"
              selectType="checkbox"
              link={{
                href: `/${zone}/keypairs`,
                title: '新增SSH密钥',
              }}
              renderSelectItem={(item: Keypair) => {
                const { keypair_name, keypair_id } = item;
                return <span>{`${keypair_name || '-'}/${keypair_id}`}</span>;
              }}
              onSelect={(newData: React.Key[], cancelData: React.Key[]) => {
                setNewSelected(newData);
                field.onChange(newData);
              }}
            />
            {errors.keypairs && (
              <portal.div css={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>
                {errors.keypairs.message}
              </portal.div>
            )}
          </FormField>
        )}
      />
    </>
  );
}

// 新的 action 函数，使用新 Modal 组件
export const loadSSHKeyNew = async (instance: Instance, onRefresh?: () => void) => {
  try {
    const result = await Modal.form<FormData>({
      title: t('加载 SSH 秘钥', 'Load SSH key'),
      width: 700,
      defaultValues: {
        keypairs: [],
        disable_ssh_passwd: false,
      },
      children: <LoadSSHKeyForm instance={instance} />,
      onAsyncOk: async (data) => {
        const zone = window.user.zone;
        const owner = window.user.user_id;
        const project_id = window.global_info.project_id;

        await apiRequest({
          params: {
            action: 'AttachKeyPairs',
            zone,
            owner,
            instances: [instance.instance_id],
            keypairs: data.keypairs,
            disable_ssh_passwd: data.disable_ssh_passwd ? 1 : 0,
            project_id,
          },
        });

        onRefresh?.();
      },
    });

    console.log('加载SSH密钥成功:', result);
  } catch (error) {
    console.log('用户取消加载或操作失败:', error);
  }
};

export default LoadSSHKeyForm;
