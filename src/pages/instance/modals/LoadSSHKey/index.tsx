import * as React from 'react';

import { Table } from '@pitrix/portal-ui';
import { requestDescribeKeyPairs, FormField, t, Modal } from '@pitrix/portal-widget';
import type { Keypair } from '@pitrix/portal-widget';

import { Instance } from 'api/types';
import { ModalTable } from 'common';
import { apiRequest } from 'utils';

interface Props {
  instance: Instance;
  onFormChange: (val: { keypairs: React.Key[] }) => void;
}

export const isItemDisabled = (instances: InstanceInfoType[]) =>
  instances?.some((item) => {
    if (item.status && item?.status !== 'stopped' && item?.status !== 'running') {
      return true;
    }

    return item.extra && item.extra.hypervisor === 'bm' && item.status !== 'stopped';
  });

const ModalContent: React.FC<Props> = ({ instance, onFormChange }: Props) => {
  const [newSelected, setNewSelected] = React.useState<React.Key[]>([]);
  const [cancelSelected, setCancelSelected] = React.useState<React.Key[]>([]);
  const [disabledSSH, setDisabledSSH] = React.useState<boolean>(true);

  const instancesIds = [instance.instance_id];
  const zone = window.user.zone;
  const owner = window.user.user_id;
  const project_id = window.global_info.project_id;

  const keypairDatas = ((instance?.keypair_ids as unknown as string[]) || [])?.map((id) => ({
    resource_id: id,
    resource_name: id,
    link: `/${zone}/${id}/`,
  }));

  const attachKeyPairs = async () => {
    if (!newSelected?.length) return;
    await apiRequest({
      params: {
        action: 'AttachKeyPairs',
        zone,
        owner,
        instances: instancesIds,
        keypairs: newSelected,
        disable_ssh_passwd: disabledSSH ? 1 : 0,
        project_id,
      },
    });
  };

  const detachKeyPairs = async () => {
    if (!cancelSelected?.length) return;
    await apiRequest({
      params: {
        action: 'DetachKeyPairs',
        zone,
        owner,
        instances: instancesIds,
        keypairs: cancelSelected,
        project_id,
      },
    });
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'keypair_name',
      key: 'keypair_name',
    },
    {
      title: '加密方式',
      key: 'encrypt_method',
      dataIndex: 'encrypt_method',
    },
  ];

  const fetchData = <P,>(params: P) =>
    requestDescribeKeyPairs({
      zone,
      owner,
      offset: 0,
      limit: 10,
      ...params,
      project_id,
    });

  React.useEffect(() => {
    onFormChange({
      keypairs: newSelected,
    });
  }, [newSelected, onFormChange]);

  return (
    <>
      <FormField
        title={t('云服务器', 'Instance')}
        displayType="vertical"
        data={[{ resource_id: instance.instance_id, resource_name: instance.instance_name }]}
        titleLineHeight="20px"
      />
      <FormField
        title={t('已加载 SSH 秘钥', 'Loaded SSH keys')}
        displayType="horizontal"
        data={keypairDatas}
        titleLineHeight="20px"
      />
      <ModalTable<Keypair>
        placeholder="输入SSH密钥名称进行搜索"
        columns={columns}
        rowKey="keypair_id"
        fetchData={fetchData}
        resultKey="keypair_set"
        selectType="checkbox"
        link={{
          href: `/${zone}/keypairs`,
          title: '新增SSH密钥',
        }}
        renderSelectItem={(item: Keypair) => {
          const { keypair_name, keypair_id } = item;
          return <span>{`${keypair_name || '-'}/${keypair_id}`}</span>;
        }}
        onSelect={(newData: React.Key[], cancelData: React.Key[]) => {
          setNewSelected(newData);
          setCancelSelected(cancelData);
        }}
        // selected={(instances && instances?.length > 1 ? [] : instances?.[0]?.keypair_ids ?? []) as unknown as Keypair[]}
      />
    </>
  );
};

export const loadSSHKey = (instance: Instance, callBack: () => void) => {
  let keypair: React.Key[] = [];
  Modal.open({
    width: 700,
    title: t('加载 SSH 秘钥', 'Load SSH key'),
    content: (
      <ModalContent
        instance={instance}
        onFormChange={(keys) => {
          keypair = keys;
        }}
      />
    ),
    onOk: async () => {
      const zone = window.user.zone;
      const owner = window.user.user_id;
      const project_id = window.global_info.project_id;
      await apiRequest({
        params: {
          action: 'AttachKeyPairs',
          zone,
          owner,
          instances: instance.instance_id,
          keypair,
          disable_ssh_passwd: 0,
          project_id,
        },
      });
      callBack?.();
    },
  });
};

export default ModalContent;
