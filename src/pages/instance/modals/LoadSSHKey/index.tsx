import React, { useState } from 'react';
import { useController } from 'react-hook-form';
import { Table, Alert } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';

import { Modal, FormField, TableToolbar, SelectionBox } from 'components';
import { useDescribeKeyPairs } from 'api/services';
import type { Instance, KeyPair } from 'api/types';

import { apiRequest } from 'utils';

interface FormData {
  keypairs: React.Key[];
  disable_ssh_passwd: boolean;
}

interface Props {
  isUnload?: boolean;
  instance: Instance;
}

// 表单内容组件
function LoadSSHKeyForm({ instance, isUnload = false }: Props): JSX.Element {
  const [searchWord, setSearchWord] = React.useState('');
  const [selectedRows, setSelectedRows] = useState<KeyPair[]>([]);

  const zone = window.user.zone;
  const owner = window.user.user_id;
  const project_id = window.global_info.project_id;

  const keypairDatas = ((instance?.keypair_ids as unknown as string[]) || [])?.map((id) => ({
    resource_id: id,
    resource_name: id,
    link: `/${zone}/${id}/`,
  }));

  const { field: { onChange, value } } = useController({
    name: 'keypairs',
    defaultValue: [],
    rules: {
      required: true,
    },
  });

  const columns = [
    {
      title: '名称',
      dataIndex: 'keypair_name',
      key: 'keypair_name',
    },
    {
      title: '加密方式',
      key: 'encrypt_method',
      dataIndex: 'encrypt_method',
    },
  ];

  const { data, isFetching, refetch } = useDescribeKeyPairs({
    zone,
    owner,
    offset: 0,
    limit: 10,
    project_id,
    search_word: searchWord,
    keypairs: isUnload ? instance?.keypair_ids : undefined,
  }, {
    enabled: true,
    select(_data) {
      return _data.keypair_set || [];
    },
  });

  return (
    <>
      <Alert css={{ marginBottom: 16 }} closable={false} type="warning" description={t('ubuntu 系统密钥加载后，请使用 root 免密登录。', 'After the key is loaded, please use root to log in without password.')} />
      <FormField
        title={t('云服务器', 'Instance')}
        displayType="vertical"
        data={[{ resource_id: instance.instance_id, resource_name: instance.instance_name }]}
        titleLineHeight="20px"
      />

      <FormField
        title={t('已加载 SSH 秘钥', 'Loaded SSH keys')}
        displayType="horizontal"
        data={keypairDatas}
        titleLineHeight="20px"
      />
      <TableToolbar
        placeholder={t('输入 SSH 密钥名称 / ID 进行搜索', 'Enter SSH key name / ID to search')}
        refresh={refetch}
        onSearch={(v) => setSearchWord(v)}
        link={{
          href: `/${zone}/keypairs`,
          title: t('新增 SSH 密钥', 'Add SSH Key'),
        }}
      />

      <Table
        placeholder="输入SSH密钥名称进行搜索"
        columns={columns}
        rowKey="keypair_id"
        dataSource={data || []}
        loading={isFetching}
        selectionType="multiple"
        selectedRowKeys={value}
        disabledRowKeys={isUnload ? [] : instance?.keypair_ids as unknown as string[]}
        onSelectionChange={(keys: string[], rows: KeyPair[]) => {
          onChange(keys);
          setSelectedRows(rows);
        }}
      />
      <SelectionBox
        text={isUnload ? t('待卸载', 'To be unloaded') : t('待加载', 'To be loaded')}
        unit={t('项', 'items')}
        options={selectedRows.map((item) => ({ value: item.keypair_id, label: item.keypair_name }))}
        onItemDelete={(option) => {
          const newRows = selectedRows.filter((item) => item.keypair_id !== option.value);
          setSelectedRows(newRows);
          onChange(newRows.map((item) => item.keypair_id));
        }}
      />
    </>
  );
}

export const loadSSHKeyAction = async (instance: Instance, onRefresh?: () => void) => {
  Modal.form<FormData>({
    title: t('加载 SSH 秘钥', 'Load SSH key'),
    width: 700,
    defaultValues: {
      keypairs: [],
      disable_ssh_passwd: false,
    },
    children: <LoadSSHKeyForm instance={instance} />,
    onAsyncOk: async (data) => {
      const zone = window.user.zone;
      const owner = window.user.user_id;
      const project_id = window.global_info.project_id;

      await apiRequest({
        params: {
          action: 'AttachKeyPairs',
          zone,
          owner,
          instances: [instance.instance_id],
          keypairs: data.keypairs,
          disable_ssh_passwd: data.disable_ssh_passwd ? 1 : 0,
          project_id,
        },
      });

      onRefresh?.();
    },
  });
};

export const unloadSSHKeyAction = async (instance: Instance, onRefresh?: () => void) => {
  Modal.form<FormData>({
    title: t('卸载 SSH 秘钥', 'Unload SSH key'),
    width: 700,
    defaultValues: {
      keypairs: [],
      disable_ssh_passwd: false,
    },
    children: <LoadSSHKeyForm isUnload instance={instance} />,
    onAsyncOk: async (data) => {
      const zone = window.user.zone;
      const owner = window.user.user_id;
      const project_id = window.global_info.project_id;

      await apiRequest({
        params: {
          action: 'DetachKeyPairs',
          zone,
          owner,
          instances: [instance.instance_id],
          keypairs: data.keypairs,
          project_id,
        },
      });

      onRefresh?.();
    },
  });
};

export default LoadSSHKeyForm;
