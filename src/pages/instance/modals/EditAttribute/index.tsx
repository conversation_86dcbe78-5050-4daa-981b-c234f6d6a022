import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Input, TextArea, portal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';

import { FormField } from 'components';
import Modal from 'components/Modal';
import { Instance } from 'api/types';
import { request } from 'common/utils';

interface FormData {
  instance_name: string;
  description: string;
}

interface Props {
  instance: Instance;
}

// 表单内容组件
function EditAttributeForm({ instance }: Props): JSX.Element {
  const { control, formState: { errors } } = useFormContext<FormData>();

  return (
    <>
      <FormField
        title={t('云服务器', 'Instance')}
        displayType="vertical"
        data={[{ resource_id: instance.instance_id, resource_name: instance.instance_name }]}
        titleLineHeight="20px"
      />

      <Controller
        name="instance_name"
        control={control}
        rules={{
          required: t('请输入名称', 'Please enter name'),
          minLength: {
            value: 1,
            message: t('名称长度不能少于1个字符', 'Name length cannot be less than 1 character'),
          },
          maxLength: {
            value: 64,
            message: t('名称长度不能超过64个字符', 'Name length cannot exceed 64 characters'),
          },
        }}
        render={({ field }) => (
          <FormField title={t('名称', 'Name')}>
            <Input {...field} placeholder={t('请输入名称', 'Please enter name')} />
            {errors.instance_name && (
              <portal.div css={{ color: 'red.11', fontSize: '12px', marginTop: '4px' }}>
                {errors.instance_name.message}
              </portal.div>
            )}
            <portal.div css={{ color: 'text.placeholder', marginTop: 4 }}>
              {t('名称长度范围为 1~64 个字符。', 'Name length must be between 1 and 64 characters.')}
            </portal.div>
          </FormField>
        )}
      />

      <Controller
        name="description"
        control={control}
        render={({ field }) => (
          <FormField title={t('描述', 'Describe')}>
            <TextArea
              {...field}
              css={{ width: '328px!important' }}
              resize="vertical"
              placeholder={t('请输入内容', 'Please enter content')}
            />
            {errors.description && (
              <portal.div css={{ color: 'red.11', fontSize: '12px', marginTop: '4px' }}>
                {errors.description.message}
              </portal.div>
            )}
          </FormField>
        )}
      />
    </>
  );
}

// 新的 action 函数，使用新 Modal 组件
export const editAttributeAction = async (instance: Instance, onRefresh?: () => void) => {
  Modal.form<FormData>({
    title: t('修改属性', 'Edit Attribute'),
    width: 520,
    defaultValues: {
      instance_name: instance.instance_name || '',
      description: instance.description || '',
    },
    children: <EditAttributeForm instance={instance} />,
    onAsyncOk: async (data) => {
      await request({
        params: {
          action: 'ModifyInstanceAttributes',
          instance: instance.instance_id,
          zone: window.user.zone,
          owner: window.user.user_id,
          ...data,
        },
      });
      onRefresh?.();
    },
  });
};

export default EditAttributeForm;
