import React from 'react';
import { Controller } from 'react-hook-form';
import { Select, Button, Link, portal, Modal as UiModal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';

import { useDescribeInstanceGroups } from 'api/services';
import type { Instance } from 'api/types';

import { Modal, FormField } from 'components';
import apiRequest from 'utils/request';

interface Props {
  instance: Instance;
}

interface FormData {
  instance_group: string;
}

const FILTERS = [
  {
    text: t('全部', 'All'),
    value: ['repel', 'attract'],
  },
  {
    text: t('分散', 'Disperse'),
    value: 'repel',
  },
  {
    text: t('集中', 'Concentrate'),
    value: 'attract',
  },
];

function InstanceGroup({ instance }: Props) {
  const zone = window.user.zone;
  const owner = window.user.user_id;
  const project_id = window.global_info.project_id || undefined;

  // const {
  //   formState: { errors },
  // } = useFormContext();

  const { data, isFetching, refetch } = useDescribeInstanceGroups(
    {
      zone,
      owner,
      project_id,
      offset: 0,
      limit: 100,
      reverse: 1,
      sort_key: 'create_time',
    },
    {
      enabled: !!zone && !!owner,
      select: (_data) => {
        return _data.map((item) => ({
          label: `${item.instance_group_name} | ${item.instance_group_id} | ${FILTERS.find((filter) => filter.value === item.relation)?.text}`,
          value: item.instance_group_id,
        }));
      },
    },
  );

  return (
    <div>
      <FormField
        labelWidth="72px"
        title={t('云服务器', 'Instance')}
        displayType="vertical"
        data={[{ resource_id: instance.instance_id, resource_name: instance.instance_name }]}
        titleLineHeight="20px"
      />
      <FormField title={t('安置策略组', 'Instance Group')} labelWidth="72px">
        <portal.div css={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Controller
            name="instance_group"
            render={({ field }) => (
              <Select
                width={328}
                loading={isFetching}
                options={data}
                value={field.value}
                onChange={field.onChange as () => void}
              />
            )}
          />
          <Button icon="Refresh2Fill" onClick={() => refetch()} />
          <Link href={`/${zone}/instance/instance_groups/`} iconName="ShareBoxFill" target="_blank" iconPlacement="after">
            {t('新增安置策略组', 'Create a new placement policy group')}
          </Link>
        </portal.div>
      </FormField>
    </div>
  );
}

export const joinInstanceGroupAction = async (instance: Instance, onRefresh?: () => void) => {
  Modal.form<FormData>({
    title: t('加入安置策略组', 'Join Instance Group'),
    width: 600,
    children: <InstanceGroup instance={instance} />,
    onAsyncOk: async (_data: FormData) => {
      await apiRequest({
        params: {
          action: 'JoinInstanceGroup',
          zone: window.user.zone,
          owner: window.user.user_id,
          instances: [instance.instance_id],
          instance_group: _data.instance_group,
        },
      });
      onRefresh?.();
    },
  });
};

export const leaveInstanceGroupAction = async (instance: Instance, onRefresh?: () => void) => {
  UiModal.alert({
    id: '#micro-iaas-root',
    title: t(`确定要离开${instance?.instance_group}吗？`, `Are you sure you want to leave ${instance?.instance_group}?`),
    onOk: async () => {
      await apiRequest({
        params: {
          action: 'LeaveInstanceGroup',
          zone: window.user.zone,
          owner: window.user.user_id,
          instances: [instance.instance_id],
          instance_group: instance.instance_group,
        },
      });
      onRefresh?.();
    },
  });
};

export default InstanceGroup;
