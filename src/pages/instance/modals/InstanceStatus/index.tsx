import React from 'react';
import { Controller } from 'react-hook-form';
import { Select, Button, Link, portal, Modal as UiModal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';

import apiRequest from 'utils/request';
import type { Instance } from 'api/types';

export function startInstanceAction(instance: Instance, onRefresh?: () => void) {
  UiModal.info({
    id: '#micro-iaas-root',
    title: t(
      `确定要对云服务器“${instance.instance_name}/${instance.instance_id}”进行开机吗？`,
      `Are you sure you want to start the cloud server "${instance.instance_name}/${instance.instance_id}"?`,
    ),
    onOk: async () => {
      await apiRequest({
        params: {
          action: 'StartInstances',
          instances: [instance?.instance_id],
        },
      });
      onRefresh?.();
    },
  });
}

export function stopInstanceAction(instance: Instance, onRefresh?: () => void) {
  UiModal.info({
    id: '#micro-iaas-root',
    title: t(
      `确定要对云服务器“${instance.instance_name}/${instance.instance_id}”关机吗？`,
      `Are you sure you want to stop the cloud server "${instance.instance_name}/${instance.instance_id}"?`,
    ),
    onOk: async () => {
      await apiRequest({
        params: {
          action: 'StopInstances',
          instances: [instance?.instance_id],
          onlyBM: false,
          charge_mode: 'elastic'
        },
      });
      onRefresh?.();
    },
  });
}
