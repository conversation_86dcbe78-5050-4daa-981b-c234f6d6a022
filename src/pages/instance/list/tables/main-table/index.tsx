import React from 'react';
import { Table, useContextMenu } from '@pitrix/portal-ui';
// import { useContextMenu } from '@pitrix/portal-widget';
import { useAtomValue } from 'jotai';

import { useDescribeInstances } from 'api/services';
import { usePagination } from 'common/hooks';
import { viewAtom, directoryIdAtom } from '../../state';
import useColumns from '../columns';
import ContextMenu from '../context-menus';
import Example from 'components/Modal/examples'

function MainTable(): JSX.Element {
  const view = useAtomValue(viewAtom);
  const directoryId = useAtomValue(directoryIdAtom);
  const columns = useColumns();
  const { current, limit, offset, setPagination } = usePagination();

  const { data, isFetching, refetch } = useDescribeInstances(
    {
      zone: window.__EXOSKELETON__.currentZone$.value,
      owner: window?.user?.user_id,
      limit,
      offset,
      sort_key: 'create_time',
      verbose: 1,
      reverse: 1,
      mount_detail: 1,
      status: ['pending', 'running', 'stopped', 'suspended', 'rescuing'],
      directory_id: directoryId,
    },
    {
      keepPreviousData: true,
    },
  );

  const { show } = useContextMenu({
    id: 'instance-context-menu',
  });

  return (
    <>
      <Table
        dataSource={data?.instance_set || []}
        columns={columns}
        css={{ overflow: 'auto' }}
        rowKey="instance_id"
        selectionType="multiple"
        loading={isFetching}
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: true,
          total: data?.total_count ?? 0,
          current,
          pageSize: limit,
          onPaging: setPagination,
        }}
        onContextMenu={(_id, datas, _index, e) => {
          show({
            event: e,
            props: {
              instance: datas,
              onRefresh: refetch,
            },
          });
        }}
      />
      <ContextMenu onRefresh={() => refetch()} />
      <Example />
    </>
  );
}

export default MainTable;
