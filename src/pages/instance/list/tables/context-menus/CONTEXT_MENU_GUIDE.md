# 右键菜单业务逻辑处理指南

## 🎯 使用新 Modal 组件处理右键菜单的最佳实践

### 1. 整体架构设计

```
右键菜单处理流程:
用户右键点击 → 显示菜单 → 点击菜单项 → 执行 Action → 调用新 Modal 组件 → 处理业务逻辑
```

### 2. 核心优势

#### 相比旧方案的改进：
- **统一的 API**: 所有弹窗都使用相同的 Modal API
- **Promise 化**: 支持 async/await，更好的错误处理
- **类型安全**: 完整的 TypeScript 支持
- **表单集成**: 内置 react-hook-form 支持
- **自动 Loading**: 异步操作自动显示加载状态
- **一致的用户体验**: 统一的弹窗样式和交互

### 3. 实现方案

#### 方案一：Action 函数重构（推荐）

```tsx
// 新的 Action 实现
export const editAttributeAction = {
  key: 'edit_attributes',
  label: t('修改属性', 'Edit Attributes'),
  icon: 'pencil_fill',
  action: async ({ instance, onRefresh }: MenuProps) => {
    try {
      const result = await Modal.form<FormData>({
        title: t('修改属性', 'Edit Attribute'),
        defaultValues: {
          instance_name: instance.instance_name || '',
          description: instance.description || '',
        },
        children: <EditAttributeForm instance={instance} />,
        onAsyncOk: async (data) => {
          await request({
            params: {
              action: 'ModifyInstanceAttributes',
              instance: instance.instance_id,
              ...data,
            },
          });
          onRefresh?.();
        },
      });
      console.log('操作成功:', result);
    } catch (error) {
      console.log('用户取消或操作失败:', error);
    }
  },
};
```

#### 方案二：直接在菜单点击处理

```tsx
const handleMenuClick = (key: string, props: { instance: Instance }) => {
  const { instance } = props;
  
  switch (key) {
    case 'edit_attributes':
      Modal.form({
        title: '修改属性',
        children: <EditForm instance={instance} />,
        onAsyncOk: async (data) => {
          await updateInstance(data);
          onRefresh();
        },
      });
      break;
      
    case 'delete_instance':
      Modal.deleteConfirm({
        title: '删除实例',
        content: `确定要删除实例 "${instance.instance_name}" 吗？`,
        onConfirm: async () => {
          await deleteInstance(instance.instance_id);
          onRefresh();
        },
      });
      break;
  }
};
```

### 4. 不同类型弹窗的处理模式

#### 4.1 表单弹窗
```tsx
// 适用于：修改属性、加载SSH密钥等需要用户输入的操作
Modal.form<FormData>({
  title: '表单标题',
  defaultValues: { /* 默认值 */ },
  children: <FormComponent />,
  onAsyncOk: async (data) => {
    await apiCall(data);
    onRefresh();
  },
});
```

#### 4.2 确认弹窗
```tsx
// 适用于：启动、停止、重启等需要确认的操作
Modal.confirm({
  title: '操作确认',
  content: '确定要执行此操作吗？',
  onAsyncOk: async () => {
    await apiCall();
    onRefresh();
  },
});
```

#### 4.3 删除确认弹窗
```tsx
// 适用于：删除实例、卸载组件等危险操作
Modal.deleteConfirm({
  title: '删除确认',
  content: '确定要删除吗？删除后无法恢复。',
  onConfirm: async () => {
    await deleteApiCall();
    onRefresh();
  },
});
```

#### 4.4 警告弹窗
```tsx
// 适用于：有风险的操作
Modal.warning({
  title: '警告',
  content: '此操作可能会影响系统性能，确定要继续吗？',
  onConfirm: async () => {
    await riskyOperation();
    onRefresh();
  },
});
```

#### 4.5 信息提示弹窗
```tsx
// 适用于：操作结果提示
Modal.success({
  title: '操作成功',
  content: '实例已成功启动',
});

Modal.error({
  title: '操作失败',
  content: '网络连接异常，请稍后重试',
});
```

### 5. 表单组件最佳实践

```tsx
import { Controller, useFormContext } from 'react-hook-form';

function EditAttributeForm({ instance }: Props) {
  const { control, formState: { errors } } = useFormContext<FormData>();

  return (
    <>
      <Controller
        name="instance_name"
        control={control}
        rules={{
          required: '请输入名称',
          minLength: { value: 1, message: '名称不能为空' },
          maxLength: { value: 64, message: '名称长度不能超过64个字符' },
        }}
        render={({ field }) => (
          <FormField title="名称">
            <Input {...field} placeholder="请输入名称" />
            {errors.instance_name && (
              <div style={{ color: 'red', fontSize: '12px' }}>
                {errors.instance_name.message}
              </div>
            )}
          </FormField>
        )}
      />
    </>
  );
}
```

### 6. 错误处理和用户体验

```tsx
const handleAction = async () => {
  try {
    const result = await Modal.form({
      // 配置...
      onAsyncOk: async (data) => {
        // 验证数据
        if (!data.name) {
          throw new Error('名称不能为空');
        }
        
        // 调用 API
        await apiCall(data);
        
        // 成功提示
        Modal.success({
          title: '操作成功',
          content: '数据已保存',
        });
        
        onRefresh();
      },
    });
  } catch (error) {
    if (error.message !== 'User cancelled') {
      // 显示错误信息
      Modal.error({
        title: '操作失败',
        content: error.message || '未知错误',
      });
    }
  }
};
```

### 7. 迁移建议

#### 7.1 渐进式迁移
1. 先迁移简单的确认弹窗
2. 再迁移表单弹窗
3. 最后处理复杂的业务逻辑

#### 7.2 保持兼容性
```tsx
// 可以同时保留新旧两套实现
export const editAttributeActionOld = (instance, onRefresh) => {
  // 旧的实现
};

export const editAttributeActionNew = async (instance, onRefresh) => {
  // 新的实现
};

// 根据配置选择使用哪个版本
const useNewModal = window.featureFlags?.useNewModal;
const editAction = useNewModal ? editAttributeActionNew : editAttributeActionOld;
```

### 8. 性能优化

- 使用 React.memo 优化表单组件
- 合理使用 useCallback 和 useMemo
- 避免在 Modal 中进行重复的数据请求

### 9. 测试建议

```tsx
// 单元测试示例
describe('EditAttributeAction', () => {
  it('should open modal with correct data', async () => {
    const instance = { instance_id: 'i-123', instance_name: 'test' };
    const onRefresh = jest.fn();
    
    await editAttributeAction.action({ instance, onRefresh });
    
    expect(Modal.form).toHaveBeenCalledWith({
      title: '修改属性',
      defaultValues: {
        instance_name: 'test',
        description: '',
      },
      // ...
    });
  });
});
```

这个方案提供了完整的右键菜单业务逻辑处理解决方案，既保持了代码的一致性，又提升了用户体验和开发效率。
