import React from 'react';
import { tagManagerAction, joinProjectAction, t } from '@pitrix/portal-widget';
import { ContextMenu } from '@pitrix/portal-ui';

import { Instance } from 'api/types';
import tagManager from '../../actions/tag-manager';
import joinProject from '../../actions/join-project';
import leaveProject from '../../actions/leave-project';
import editAttribute from '../../actions/edit-attribute';
import loadSshkey from '../../actions/load-sshkey';

interface MenuProps {
  onRefresh: () => void;
}

function InstanceMenus({ onRefresh }: MenuProps) {
  const menus = [
    {
      key: 'start',
      label: '主机设置',
      icon: 'gear_2_fill',
      subMenus: [
        editAttribute,
        loadSshkey,
        {
          key: 'unloadSSHKey',
          label: '卸载 SSH 密钥',
          icon: 'chain_off_fill',
        },
        {
          key: 'resetPassword',
          label: '重置登陆密码',
          icon: 'password_lock_fill',
        },
        {
          key: 'joinPlacementGroup',
          label: '加入安置策略组',
          icon: 'groups_fill',
        },
        {
          key: 'leavePlacementGroup',
          label: '离开安置策略组',
          icon: 'groups_fill',
        },
        {
          key: 'enableSyncTime',
          label: '启用同步时间',
          icon: 'lock_fill',
        },
        {
          key: 'disableSyncTime',
          label: '关闭同步时间',
          icon: 'lock_fill',
        },
        {
          key: 'rescue',
          label: '救援云服务器',
          icon: 'medicine_chest_fill',
        },
        {
          key: 'cancelRescue',
          label: '取消救援模式',
          icon: 'medicine_chest_fill',
        },
        {
          key: 'migrate',
          label: '移动到目录',
          icon: 'topology_2_fill',
        },
      ],
    },
    {
      key: 'instanceStatus',
      label: '主机状态',
      icon: 'server_fill',
      subMenus: [
        {
          key: 'stop',
          label: '关机',
          icon: 'shutdown_2_fill',
        },
        {
          key: 'start',
          label: '开机',
          icon: 'play_fill',
        },
        {
          key: 'reboot',
          label: '重启',
          icon: 'refresh_2_fill',
        },
        {
          key: 'suspend',
          separator: true,
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'trash_fill',
        },
      ],
    },
    {
      key: 'network',
      label: '网络',
      icon: 'network_fill',
      disabled: '网络功能未启用',
    },
    {
      key: 'publicIp',
      label: '公网 IP',
      icon: 'eip_fill',
    },
    {
      key: 'securityGroup',
      label: '安全组',
      icon: 'security_combination_fill',
    },
    {
      key: 'volAndImage',
      label: '硬盘与镜像',
      icon: 'hard_disk_3_fill',
    },
    {
      key: 'backupAndClone',
      label: '备份与克隆',
      icon: 'note_copy_fill',
    },
    {
      key: 'resourceOrchestration',
      label: '资源变配',
      icon: 'resource_orchestration_fill',
    },
    {
      key: 'chargeMode',
      label: '计费模式',
      icon: 'rmb_circle_fill',
    },
    {
      key: 'tagsAndProject',
      label: '标签与项目',
      icon: 'tags_fill',
      subMenus: [tagManager, joinProject, leaveProject],
    },
    {
      key: 'cdrom',
      label: '光驱管理',
      icon: 'os_service_fill',
    },
  ];

  return (
    <ContextMenu
      id="instance-context-menu"
      menus={menus}
      onMenuClick={(key, props) => {
        console.log(key, props);
      }}
    />
  );
}

export default InstanceMenus;
