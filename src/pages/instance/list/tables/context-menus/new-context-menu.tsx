import React from 'react';
import { t } from '@pitrix/portal-widget';
import { ContextMenu } from '@pitrix/portal-ui';

import { Instance } from 'api/types';
import { newInstanceActions } from '../../actions/new-actions';

interface MenuProps {
  onRefresh: () => void;
  instance: Instance;
}

interface ContextMenuProps {
  onRefresh: () => void;
}

function NewInstanceContextMenu({ onRefresh }: ContextMenuProps) {
  // 构建菜单配置
  const buildMenus = (instance: Instance) => [
    {
      key: 'settings',
      label: t('主机设置', 'Instance Settings'),
      icon: 'gear_2_fill',
      subMenus: [
        {
          key: 'edit_attributes',
          label: newInstanceActions.editAttribute.label,
          icon: newInstanceActions.editAttribute.icon,
          action: () => newInstanceActions.editAttribute.action({ instance, onRefresh }),
        },
        {
          key: 'load_ssh_key',
          label: newInstanceActions.loadSSHKey.label,
          icon: newInstanceActions.loadSSHKey.icon,
          action: () => newInstanceActions.loadSSHKey.action({ instance, onRefresh }),
        },
        {
          key: 'unload_ssh_key',
          label: newInstanceActions.unloadSSHKey.label,
          icon: newInstanceActions.unloadSSHKey.icon,
          action: () => newInstanceActions.unloadSSHKey.action({ instance, onRefresh }),
        },
        {
          key: 'reset_password',
          label: newInstanceActions.resetPassword.label,
          icon: newInstanceActions.resetPassword.icon,
          action: () => newInstanceActions.resetPassword.action({ instance, onRefresh }),
        },
      ],
    },
    {
      key: 'status',
      label: t('主机状态', 'Instance Status'),
      icon: 'server_fill',
      subMenus: [
        {
          key: 'start_instance',
          label: newInstanceActions.startInstance.label,
          icon: newInstanceActions.startInstance.icon,
          disabled: newInstanceActions.startInstance.disabled?.({ instance, onRefresh }),
          action: () => newInstanceActions.startInstance.action({ instance, onRefresh }),
        },
        {
          key: 'stop_instance',
          label: newInstanceActions.stopInstance.label,
          icon: newInstanceActions.stopInstance.icon,
          disabled: newInstanceActions.stopInstance.disabled?.({ instance, onRefresh }),
          action: () => newInstanceActions.stopInstance.action({ instance, onRefresh }),
        },
        {
          key: 'reboot_instance',
          label: newInstanceActions.rebootInstance.label,
          icon: newInstanceActions.rebootInstance.icon,
          disabled: newInstanceActions.rebootInstance.disabled?.({ instance, onRefresh }),
          action: () => newInstanceActions.rebootInstance.action({ instance, onRefresh }),
        },
        {
          key: 'separator',
          separator: true,
        },
        {
          key: 'delete_instance',
          label: newInstanceActions.deleteInstance.label,
          icon: newInstanceActions.deleteInstance.icon,
          action: () => newInstanceActions.deleteInstance.action({ instance, onRefresh }),
        },
      ],
    },
    {
      key: 'network',
      label: t('网络', 'Network'),
      icon: 'network_fill',
      disabled: t('网络功能未启用', 'Network function not enabled'),
    },
    {
      key: 'publicIp',
      label: t('公网 IP', 'Public IP'),
      icon: 'eip_fill',
    },
    {
      key: 'securityGroup',
      label: t('安全组', 'Security Group'),
      icon: 'security_combination_fill',
    },
    {
      key: 'volAndImage',
      label: t('硬盘与镜像', 'Volume and Image'),
      icon: 'hard_disk_3_fill',
    },
    {
      key: 'backupAndClone',
      label: t('备份与克隆', 'Backup and Clone'),
      icon: 'note_copy_fill',
    },
    {
      key: 'resourceOrchestration',
      label: t('资源变配', 'Resource Orchestration'),
      icon: 'resource_orchestration_fill',
    },
    {
      key: 'chargeMode',
      label: t('计费模式', 'Charge Mode'),
      icon: 'rmb_circle_fill',
    },
    {
      key: 'cdrom',
      label: t('光驱管理', 'CD-ROM Management'),
      icon: 'os_service_fill',
    },
  ];

  // 处理菜单点击事件
  const handleMenuClick = (key: string, props: { instance: Instance }) => {
    const { instance } = props;
    const menus = buildMenus(instance);
    
    // 递归查找并执行对应的 action
    const findAndExecuteAction = (menuItems: any[], targetKey: string): boolean => {
      for (const item of menuItems) {
        if (item.key === targetKey && item.action) {
          item.action();
          return true;
        }
        if (item.subMenus && findAndExecuteAction(item.subMenus, targetKey)) {
          return true;
        }
      }
      return false;
    };

    const executed = findAndExecuteAction(menus, key);
    if (!executed) {
      console.log(`未找到对应的处理函数: ${key}`, props);
    }
  };

  return (
    <ContextMenu
      id="new-instance-context-menu"
      menus={[]} // 这里需要根据具体实例动态生成
      onMenuClick={handleMenuClick}
    />
  );
}

// 导出一个工厂函数，用于创建带有实例信息的右键菜单
export const createInstanceContextMenu = (instance: Instance, onRefresh: () => void) => {
  const menus = [
    {
      key: 'settings',
      label: t('主机设置', 'Instance Settings'),
      icon: 'gear_2_fill',
      subMenus: [
        {
          key: 'edit_attributes',
          label: newInstanceActions.editAttribute.label,
          icon: newInstanceActions.editAttribute.icon,
        },
        {
          key: 'load_ssh_key',
          label: newInstanceActions.loadSSHKey.label,
          icon: newInstanceActions.loadSSHKey.icon,
        },
        {
          key: 'unload_ssh_key',
          label: newInstanceActions.unloadSSHKey.label,
          icon: newInstanceActions.unloadSSHKey.icon,
        },
        {
          key: 'reset_password',
          label: newInstanceActions.resetPassword.label,
          icon: newInstanceActions.resetPassword.icon,
        },
      ],
    },
    {
      key: 'status',
      label: t('主机状态', 'Instance Status'),
      icon: 'server_fill',
      subMenus: [
        {
          key: 'start_instance',
          label: newInstanceActions.startInstance.label,
          icon: newInstanceActions.startInstance.icon,
          disabled: newInstanceActions.startInstance.disabled?.({ instance, onRefresh }),
        },
        {
          key: 'stop_instance',
          label: newInstanceActions.stopInstance.label,
          icon: newInstanceActions.stopInstance.icon,
          disabled: newInstanceActions.stopInstance.disabled?.({ instance, onRefresh }),
        },
        {
          key: 'reboot_instance',
          label: newInstanceActions.rebootInstance.label,
          icon: newInstanceActions.rebootInstance.icon,
          disabled: newInstanceActions.rebootInstance.disabled?.({ instance, onRefresh }),
        },
        {
          key: 'separator',
          separator: true,
        },
        {
          key: 'delete_instance',
          label: newInstanceActions.deleteInstance.label,
          icon: newInstanceActions.deleteInstance.icon,
        },
      ],
    },
  ];

  const handleMenuClick = (key: string) => {
    const actionMap: Record<string, () => void> = {
      edit_attributes: () => newInstanceActions.editAttribute.action({ instance, onRefresh }),
      load_ssh_key: () => newInstanceActions.loadSSHKey.action({ instance, onRefresh }),
      unload_ssh_key: () => newInstanceActions.unloadSSHKey.action({ instance, onRefresh }),
      reset_password: () => newInstanceActions.resetPassword.action({ instance, onRefresh }),
      start_instance: () => newInstanceActions.startInstance.action({ instance, onRefresh }),
      stop_instance: () => newInstanceActions.stopInstance.action({ instance, onRefresh }),
      reboot_instance: () => newInstanceActions.rebootInstance.action({ instance, onRefresh }),
      delete_instance: () => newInstanceActions.deleteInstance.action({ instance, onRefresh }),
    };

    const action = actionMap[key];
    if (action) {
      action();
    } else {
      console.log(`未实现的菜单项: ${key}`);
    }
  };

  return {
    menus,
    handleMenuClick,
  };
};

export default NewInstanceContextMenu;
