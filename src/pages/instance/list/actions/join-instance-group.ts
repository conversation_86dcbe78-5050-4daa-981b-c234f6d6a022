import { t } from '@pitrix/portal-widget';

import { joinInstanceGroupAction } from 'pages/instance/modals/InstanceGroup';
import type { MenuProps } from './type';

export default {
  key: 'join_instance_group',
  label: t('加入安置策略组', 'Join Instance Group'),
  icon: 'groups_fill',
  disabled: ({ instance }: MenuProps) => {
    return instance?.instance_group;
  },
  action: ({ instance, onRefresh }: MenuProps) => {
    joinInstanceGroupAction(instance, onRefresh);
  },
};
