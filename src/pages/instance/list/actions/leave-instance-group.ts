import { t } from '@pitrix/portal-widget';

import { leaveInstanceGroupAction } from 'pages/instance/modals/InstanceGroup';
import type { MenuProps } from './type';

export default {
  key: 'leave_instance_group',
  label: t('离开安置策略组', 'Leave Instance Group'),
  icon: 'groups_fill',
  disabled: ({ instance }: MenuProps) => {
    return !instance?.instance_group;
  },
  action: ({ instance, onRefresh }: MenuProps) => {
    leaveInstanceGroupAction(instance, onRefresh);
  },
};
