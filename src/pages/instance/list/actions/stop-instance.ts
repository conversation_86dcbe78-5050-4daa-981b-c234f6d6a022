import { t } from '@pitrix/portal-widget';

import { stopInstanceAction } from 'pages/instance/modals/InstanceStatus';
import type { MenuProps } from './type';

export default {
  key: 'stop_instance',
  label: t('关机', 'Stop Instance'),
  icon: 'shutdown_2_fill',
  disabled: ({ instance }: MenuProps) => instance?.status === 'stopped',
  action: ({ instance, onRefresh }: MenuProps) => {
    stopInstanceAction(instance, onRefresh);
  },
};
