import { t } from '@pitrix/portal-widget';

import { unloadSSHKeyAction } from 'pages/instance/modals/LoadSSHKey';
import type { MenuProps } from './type';

export default {
  key: 'unload_ssh_key',
  label: t('卸载 SSH 秘钥', 'Load SSH key'),
  icon: 'chain_off_fill',
  disabled: ({ instance }: MenuProps) => {
    return instance?.keypair_ids?.length === 0;
  },
  action: ({ instance, onRefresh }: MenuProps) => {
    unloadSSHKeyAction(instance, onRefresh);
  },
};
