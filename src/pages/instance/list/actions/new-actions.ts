import { t } from '@pitrix/portal-widget';
import Modal from 'components/Modal';
import { Instance } from 'api/types';
import { editAttributeActionNew } from 'pages/instance/modals/EditAttribute/new-index';
import { loadSSHKeyNew } from 'pages/instance/modals/LoadSSHKey/new-index';
import { request } from 'common/utils';

export interface MenuProps {
  instance: Instance;
  onRefresh: () => void;
}

// 使用新 Modal 组件的 Actions

// 1. 修改属性
export const editAttributeAction = {
  key: 'edit_attributes',
  label: t('修改属性', 'Edit Attributes'),
  icon: 'pencil_fill',
  action: async ({ instance, onRefresh }: MenuProps) => {
    await editAttributeActionNew(instance, onRefresh);
  },
};

// 2. 加载SSH密钥
export const loadSSHKeyAction = {
  key: 'load_ssh_key',
  label: t('加载 SSH 秘钥', 'Load SSH key'),
  icon: 'pencil_fill',
  action: async ({ instance, onRefresh }: MenuProps) => {
    await loadSSHKeyNew(instance, onRefresh);
  },
};

// 3. 卸载SSH密钥
export const unloadSSHKeyAction = {
  key: 'unload_ssh_key',
  label: t('卸载 SSH 密钥', 'Unload SSH key'),
  icon: 'chain_off_fill',
  action: async ({ instance, onRefresh }: MenuProps) => {
    try {
      await Modal.deleteConfirm({
        title: t('卸载 SSH 密钥', 'Unload SSH key'),
        content: t('确定要卸载该实例的所有SSH密钥吗？', 'Are you sure you want to unload all SSH keys for this instance?'),
        onConfirm: async () => {
          await request({
            params: {
              action: 'DetachKeyPairs',
              zone: window.user.zone,
              owner: window.user.user_id,
              instances: [instance.instance_id],
              keypairs: instance.keypair_ids || [],
              project_id: window.global_info.project_id,
            },
          });
          onRefresh?.();
        },
      });
    } catch (error) {
      console.log('用户取消卸载:', error);
    }
  },
};

// 4. 重置登录密码
export const resetPasswordAction = {
  key: 'reset_password',
  label: t('重置登陆密码', 'Reset Login Password'),
  icon: 'password_lock_fill',
  action: async ({ instance, onRefresh }: MenuProps) => {
    try {
      const result = await Modal.form<{ new_password: string; confirm_password: string }>({
        title: t('重置登陆密码', 'Reset Login Password'),
        width: 520,
        defaultValues: {
          new_password: '',
          confirm_password: '',
        },
        children: (
          <div style={{ padding: '20px 0' }}>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '4px' }}>
                {t('新密码', 'New Password')}
              </label>
              <input
                type="password"
                placeholder={t('请输入新密码', 'Please enter new password')}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                }}
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '4px' }}>
                {t('确认密码', 'Confirm Password')}
              </label>
              <input
                type="password"
                placeholder={t('请再次输入密码', 'Please enter password again')}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                }}
              />
            </div>
          </div>
        ),
        onAsyncOk: async (data) => {
          if (data.new_password !== data.confirm_password) {
            throw new Error(t('两次输入的密码不一致', 'The passwords entered twice are inconsistent'));
          }
          
          await request({
            params: {
              action: 'ResetInstancesPassword',
              zone: window.user.zone,
              owner: window.user.user_id,
              instances: [instance.instance_id],
              login_passwd: data.new_password,
              project_id: window.global_info.project_id,
            },
          });
          onRefresh?.();
        },
      });
      
      console.log('重置密码成功:', result);
    } catch (error) {
      console.log('用户取消重置或操作失败:', error);
    }
  },
};

// 5. 删除实例
export const deleteInstanceAction = {
  key: 'delete_instance',
  label: t('删除', 'Delete'),
  icon: 'trash_fill',
  action: async ({ instance, onRefresh }: MenuProps) => {
    try {
      await Modal.deleteConfirm({
        title: t('删除实例', 'Delete Instance'),
        content: t(
          `确定要删除实例 "${instance.instance_name}" 吗？删除后无法恢复。`,
          `Are you sure you want to delete instance "${instance.instance_name}"? This action cannot be undone.`
        ),
        onConfirm: async () => {
          await request({
            params: {
              action: 'TerminateInstances',
              zone: window.user.zone,
              owner: window.user.user_id,
              instances: [instance.instance_id],
              project_id: window.global_info.project_id,
            },
          });
          onRefresh?.();
        },
      });
    } catch (error) {
      console.log('用户取消删除:', error);
    }
  },
};

// 6. 启动实例
export const startInstanceAction = {
  key: 'start_instance',
  label: t('开机', 'Start'),
  icon: 'play_fill',
  disabled: ({ instance }: MenuProps) => instance.status === 'running',
  action: async ({ instance, onRefresh }: MenuProps) => {
    try {
      await Modal.confirm({
        title: t('启动实例', 'Start Instance'),
        content: t(`确定要启动实例 "${instance.instance_name}" 吗？`, `Are you sure you want to start instance "${instance.instance_name}"?`),
        onAsyncOk: async () => {
          await request({
            params: {
              action: 'RunInstances',
              zone: window.user.zone,
              owner: window.user.user_id,
              instances: [instance.instance_id],
              project_id: window.global_info.project_id,
            },
          });
          onRefresh?.();
        },
      });
    } catch (error) {
      console.log('用户取消启动:', error);
    }
  },
};

// 7. 停止实例
export const stopInstanceAction = {
  key: 'stop_instance',
  label: t('关机', 'Stop'),
  icon: 'shutdown_2_fill',
  disabled: ({ instance }: MenuProps) => instance.status === 'stopped',
  action: async ({ instance, onRefresh }: MenuProps) => {
    try {
      await Modal.warning({
        title: t('停止实例', 'Stop Instance'),
        content: t(`确定要停止实例 "${instance.instance_name}" 吗？`, `Are you sure you want to stop instance "${instance.instance_name}"?`),
        onConfirm: async () => {
          await request({
            params: {
              action: 'StopInstances',
              zone: window.user.zone,
              owner: window.user.user_id,
              instances: [instance.instance_id],
              project_id: window.global_info.project_id,
            },
          });
          onRefresh?.();
        },
      });
    } catch (error) {
      console.log('用户取消停止:', error);
    }
  },
};

// 8. 重启实例
export const rebootInstanceAction = {
  key: 'reboot_instance',
  label: t('重启', 'Reboot'),
  icon: 'refresh_2_fill',
  disabled: ({ instance }: MenuProps) => instance.status !== 'running',
  action: async ({ instance, onRefresh }: MenuProps) => {
    try {
      await Modal.warning({
        title: t('重启实例', 'Reboot Instance'),
        content: t(`确定要重启实例 "${instance.instance_name}" 吗？`, `Are you sure you want to reboot instance "${instance.instance_name}"?`),
        onConfirm: async () => {
          await request({
            params: {
              action: 'RestartInstances',
              zone: window.user.zone,
              owner: window.user.user_id,
              instances: [instance.instance_id],
              project_id: window.global_info.project_id,
            },
          });
          onRefresh?.();
        },
      });
    } catch (error) {
      console.log('用户取消重启:', error);
    }
  },
};

// 导出所有 actions
export const newInstanceActions = {
  editAttribute: editAttributeAction,
  loadSSHKey: loadSSHKeyAction,
  unloadSSHKey: unloadSSHKeyAction,
  resetPassword: resetPasswordAction,
  deleteInstance: deleteInstanceAction,
  startInstance: startInstanceAction,
  stopInstance: stopInstanceAction,
  rebootInstance: rebootInstanceAction,
};
