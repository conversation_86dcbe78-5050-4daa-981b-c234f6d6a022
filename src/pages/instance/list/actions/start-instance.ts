import { t } from '@pitrix/portal-widget';

import { startInstanceAction } from 'pages/instance/modals/InstanceStatus';
import type { MenuProps } from './type';

export default {
  key: 'start_instance',
  label: t('开机', 'Start Instance'),
  icon: 'play_fill',
  disabled: ({ instance }: MenuProps) => instance?.status === 'running',
  action: ({ instance, onRefresh }: MenuProps) => {
    startInstanceAction(instance, onRefresh);
  },
};
