import { LazyExoticComponent, lazy } from 'react';

/**
 * 主机弹窗
 */
const ADDINS_MAPPING: Record<string, LazyExoticComponent<() => JSX.Element | null>> = {
  OamSelectInstanceTypes: lazy(() => import('./instance/select-instance-type')),
  HotPlug: lazy(() => import('./instance/hotplug')),
  HotPlugLimits: lazy(() => import('./instance/hotplug-limits')),
  ChangeConfiguration: lazy(() => import('./instance/change-configuration')),
  CloneVolume: lazy(() => import('./volume/clone-volume')),
  CreateVolumeByVdc: lazy(() => import('./volume/create-by-vdc')),
  MigrateInstance: lazy(() => import('./instance/migrate-instance')),
  IsoManage: lazy(() => import('./instance/iso-manage')),
  StartupManage: lazy(() => import('./instance/startup-manage')),
  ImageManage: lazy(() => import('./instance/image-manage')),
  QosSettings: lazy(() => import('./volume/qos-settings')),
  VPNManagement: lazy(() => import('./vpc/vpn-management')),
  MountOpticalDrive: lazy(() => import('./instance/mount-optical-drive')),
  unMountOpticalDrive: lazy(() => import('./instance/unmount-optical-drive')),
  SetStartupOrder: lazy(() => import('./instance/startup-manage')),
  BgpNeighbors: lazy(() => import('./vpc/bgp-neighbors')),
  RoutingTable: lazy(() => import('./vpc/routing-table')),
  VolumeSnapshots: lazy(() => import('pages/snapshot/detail-list')),
  AddLbPolicyRules: lazy(() => import('./lb/add-lb-policy-forward-rules')),
  CreateSnapshot: lazy(() => import('./instance/create-snapshot')),
  CreateBackups: lazy(() => import('./volume/create-backups')),
  ReplaceCertificatesModal: lazy(() => import('./lb/replacement-certificate-modal')),
  ReplaceCertificatesList: lazy(() => import('./lb/replacement-certificate-list')),
  ScheduledDeletion: lazy(() => import('./instance/scheduled-deletion')),
  ModifyScheduledDeletion: lazy(() => import('./instance/modify-scheduled-deletion')),
  CancelScheduledDeletion: lazy(() => import('./instance/cancel-scheduled-deletion')),
  MacVTapMulticast: lazy(() => import('./instance/macvtap-multicast')),
};

export default ADDINS_MAPPING;
