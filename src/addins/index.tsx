import React from 'react';
import ReactDOM from 'react-dom';

import { getRoot, QianKunProps } from '../common';
import App from './app';
import { MicAppContext } from '../common/hooks/qiankun';

/** 微应用启动 */
// eslint-disable-next-line @typescript-eslint/no-empty-function
export const bootstrap = async () => {};

export const mount = async (props: QianKunProps) => {
  ReactDOM.render(
    <MicAppContext.Provider value={props}>
      <App />
    </MicAppContext.Provider>,
    getRoot(props.container, 'addins'),
  );
};

export const unmount = async ({ container }: QianKunProps) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (window as any)?.jobSubscribe?.unsubscribe?.();

  ReactDOM.unmountComponentAtNode(getRoot(container, 'addins'));
};
