import React from 'react';
import { portal } from '@pitrix/portal-ui';
import { useWatch } from 'react-hook-form';

import { BackupNameField, BackupNameTaskField } from './backup-name-field';
import BillingInfo from './billing-info';
import SchedulerField from './scheduler-field';
import ResourcesField from './resources-field';
import ChainField from './chain-field';
import RuleField from './rule-field';
import Backuptype from './backup-type';
import FullChain from './full-chain';
import SchedulerType from './schedulers-type';
import NewScheduler from './new-scheduler';

export default function CreateForm({ supportSnapshot }: { supportSnapshot: boolean }): JSX.Element {
  const [autoBackup, newScheduler] = useWatch({ name: ['auto_backup', 'new_scheduler'] });

  return (
    <portal.div css={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}>
      <BillingInfo />
      {+autoBackup ? <BackupNameTaskField /> : <BackupNameField />}
      <ResourcesField />
      <Backuptype />
      {+autoBackup ? (
        <>
          <SchedulerType />
          {+newScheduler ? <NewScheduler /> : <SchedulerField />}
          <ChainField />
          <RuleField />
        </>
      ) : (
        <FullChain supportSnapshot={supportSnapshot} />
      )}
    </portal.div>
  );
}
