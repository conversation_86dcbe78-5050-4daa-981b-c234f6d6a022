import React from 'react';
import { Slider, InputNumber, portal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useController } from 'react-hook-form';

import { Field } from 'common';

function ChainField(): JSX.Element {
  const config = window.CONFIG?.valid_backup_chain_len;
  const {
    field: { value, onChange },
  } = useController({
    name: 'chain_len',
    defaultValue: config?.defaultValue ?? 30,
    shouldUnregister: true,
  });

  return (
    <Field label={t('备份链长度', 'Backup chain length')} align="left">
      <div>
        <portal.div css={{ center: true }}>
          <Slider
            min={config?.min ?? 1}
            max={config?.max ?? 30}
            step={config?.step ?? 1}
            value={value}
            onChange={onChange}
            css={{ width: 318, marginRight: 20 }}
          />
          <InputNumber
            value={value}
            min={config?.min ?? 1}
            max={config?.max ?? 30}
            step={config?.step ?? 1}
            onChange={onChange}
          />
        </portal.div>
        <portal.span css={{ color: 'text.placeholder' }}>
          {config?.min ?? 1} - {config?.max ?? 30}
        </portal.span>
      </div>
    </Field>
  );
}

export default ChainField;
