import React from 'react';
import { RadioGroup, portal, Link } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useController } from 'react-hook-form';

import { InputNumber } from 'common/components/input-number';
import { Field } from 'common';

const RULES_OPTIONS = [
  {
    label: t('永久保留', 'Permanent retention'),
    value: '0',
  },
  {
    label: t('按备份链条数', 'By number of backup chains'),
    value: '1',
  },
];
function RuleField(): JSX.Element {
  const {
    field: { value, onChange },
  } = useController({
    name: 'auto_delete',
    defaultValue: '0',
    shouldUnregister: true,
  });
  const {
    field: { value: limit, onChange: limitChange },
  } = useController({
    name: 'chain_limit',
    defaultValue: 2,
    shouldUnregister: true,
  });

  return (
    <Field label={t('保留规则', 'Reservation rules')} align="left">
      <div>
        <RadioGroup value={value} layout="vertical" onChange={onChange} options={RULES_OPTIONS} />
        <InputNumber
          min={1}
          max={100}
          value={limit}
          onChange={limitChange}
          controls="outside"
          style={{ marginTop: -10 }}
        />
        <portal.div css={{ color: 'warning.active', marginTop: 4 }}>
          {t(
            '当资源备份链配额已满时，在创建新备份链前将自动删除最早的1条备份链。注意：定时器备份链条数的保留规则计算时不包含手动创建的备份。',
            'when the resource backup chain quota is full the earliest backup chain will be automatically deleted before creating a new backup chain note the retention rule for the number of timer backup chains does not include manually created backups when calculating',
          )}
          <Link target="_blank" data-doc-link data-doc-key="doc_management_scheduler_intro">
            {t('了解更多', 'Learn more')}
          </Link>
        </portal.div>
      </div>
    </Field>
  );
}

export default RuleField;
