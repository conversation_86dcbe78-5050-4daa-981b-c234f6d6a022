import React from 'react';
import { t } from '@pitrix/portal-widget';
import { Input, portal } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';

import { Field } from 'common';

export function BackupNameField() {
  const {
    field: { value: nameValue, onChange: nameChange },
  } = useController({
    name: 'snapshot_name',
    defaultValue: '',
  });
  return (
    <Field label={t('名称', 'Name')} align="left">
      <Input
        width={328}
        value={nameValue}
        onChange={nameChange}
        maxLength={50}
        placeholder={t('请输入备份名称', 'Please enter the backup name')}
      />
    </Field>
  );
}

export function BackupNameTaskField() {
  const {
    field: { value: taskName, onChange: taskNameChange },
    formState: { errors },
  } = useController({
    name: 'scheduler_task_name',
    defaultValue: '',
    rules: {
      required: true,
    },
  });

  return (
    <Field label={t('名称', 'Name')} align="left">
      <div>
        <Input
          value={taskName}
          onChange={taskNameChange}
          placeholder={t('请输入备份任务名称', 'Please enter the backup task name')}
          error={errors.scheduler_task_name}
        />
        {!!errors.scheduler_task_name && (
          <portal.div css={{ color: 'red.10', marginTop: 4, lineHeight: '20px' }}>
            {t('请输入备份任务名称', 'Please enter the backup task name')}
          </portal.div>
        )}
      </div>
    </Field>
  );
}
