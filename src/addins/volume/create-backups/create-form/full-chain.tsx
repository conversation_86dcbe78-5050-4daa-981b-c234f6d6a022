import React from 'react';
import { t } from '@pitrix/portal-widget';
import { Checkbox } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';

import { Field } from 'common';

function FullChain({ supportSnapshot }: { supportSnapshot: boolean }): JSX.Element {
  const {
    field: { value: isFull, onChange: isFullChange },
  } = useController({
    name: 'is_full',
    defaultValue: false,
  });

  const showTip = supportSnapshot && window?.GLOBAL_CONFIG?.support_volume_snapshot;

  return (
    <Field css={{ marginTop: -18, marginBottom: 10 }}>
      <Checkbox
        value=""
        checked={isFull}
        onChange={isFullChange}
        label={t('创建一条新的全量备份链', 'Create a new full backup chain')}
        description={
          showTip
            ? t(
                '从快照回滚后，下一次备份将强制执行全量备份，无法进行增量备份。',
                'The next backup will be forced to perform a full backup after rolling back from a snapshot, and incremental backup cannot be performed.',
              )
            : undefined
        }
      />
    </Field>
  );
}

export default FullChain;
