import React from 'react';
import { t } from '@pitrix/portal-widget';
import { RadioGroup } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';

import { Field } from 'common';

const BACKUP_TYPES = [
  {
    label: t('手动备份', 'Manual Backup'),
    value: '0',
  },
  {
    label: t('自动备份', 'Automatic Backup'),
    value: '1',
  },
];
function Backuptype(): JSX.Element {
  const {
    field: { value, onChange },
  } = useController({
    name: 'auto_backup',
    defaultValue: '0',
  });
  return (
    <Field label={t('备份类型', 'Backup Type')} align="left">
      <RadioGroup value={value} onChange={onChange} options={BACKUP_TYPES} />
    </Field>
  );
}

export default Backuptype;
