import React from 'react';
import { Checkbox, portal, Link, Icon } from '@pitrix/portal-ui';
import { useAtomValue } from 'jotai';
import { t } from '@pitrix/portal-widget';
import { useController } from 'react-hook-form';

import { Instance, Volume, Field } from 'common';
import { backupInfoAtom } from '../state';

function ResourcesField(): JSX.Element {
  const info = useAtomValue(backupInfoAtom);
  const isInstance = (info?.[0] as Instance)?.instance_id !== undefined;
  const selectIds = info?.map((item) => {
    return (item as Instance)?.instance_id ?? (item as Volume)?.volume_id;
  });

  const {
    field: { value, onChange },
  } = useController({
    name: 'resources',
    defaultValue: selectIds,
  });
  const getHref = (item: Instance | Volume) => {
    const zone = window.user.zone;
    return isInstance
      ? `/${zone}/instances/instances/${(item as Instance).instance_id}`
      : `/${zone}/volumes/volumes/${(item as Volume).volume_id}`;
  };

  const handleVolumeCheck = (isCheck: boolean, id: string) => {
    const idSet = new Set(value);
    if (isCheck) {
      idSet.add(id);
    } else {
      idSet.delete(id);
    }
    onChange(Array.from(idSet));
  };

  return (
    <Field label={t('资源', 'Resource')} align="left">
      <portal.div css={{ display: 'flex', flexDirection: 'column', gap: 24 }}>
        {info?.map((item: Instance | Volume) => (
          <div key={(item as Instance)?.instance_id || (item as Volume)?.volume_id}>
            <portal.div css={{ display: 'flex', alignItems: 'center' }}>
              <Icon name={isInstance ? 'computer_fill' : 'hard_disk_3_fill'} size={14} />
              <portal.span css={{ margin: '0 2px' }}>
                {((item as Instance)?.instance_name ?? (item as Volume)?.volume_name) || '--'}
              </portal.span>
              <Link target="_blank" href={getHref(item)}>
                ({(item as Instance)?.instance_id || (item as Volume)?.volume_id})
              </Link>
            </portal.div>
            {!!isInstance &&
              !!(item as Instance)?.volumes?.length &&
              (item as Instance)?.volumes?.map((vol) => (
                <portal.div key={vol.volume_id} css={{ display: 'flex', alignItems: 'center', marginTop: 12 }}>
                  <Checkbox
                    value={vol?.volume_id}
                    checked={value?.includes(vol?.volume_id)}
                    onChange={handleVolumeCheck}
                  />
                  <Icon name="hard_disk_3_fill" size={14} style={{ margin: '0 8px' }} />
                  <span>{vol?.volume_name || '--'}</span>
                  <Link target="_blank" href={`/${window.user.zone}/volumes/volumes/${vol.volume_id}`}>
                    ({vol.volume_id})
                  </Link>
                </portal.div>
              ))}
          </div>
        ))}
      </portal.div>
    </Field>
  );
}

export default ResourcesField;
