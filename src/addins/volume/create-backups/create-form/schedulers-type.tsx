import React from 'react';
import { RadioButtonGroup } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useController } from 'react-hook-form';

import { Field } from 'common';

const OPTIONS = [
  {
    label: t('已有定时器', 'Existing Scheduler'),
    value: '0',
  },
  {
    label: t('新增定时器', 'New Scheduler'),
    value: '1',
  },
];

function SchedulerType(): JSX.Element {
  const {
    field: { value, onChange },
  } = useController({
    name: 'new_scheduler',
    defaultValue: '0',
  });

  return (
    <Field label={t('定时器', 'Scheduler')} align="left">
      <RadioButtonGroup options={OPTIONS} value={value} onChange={onChange} />
    </Field>
  );
}

export default SchedulerType;
