import React from 'react';
import { portal } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';
import { t } from '@pitrix/portal-widget';

import { Field } from 'common';
import DateSelect from './date-select';

export default function Week(): JSX.Element {
  const {
    field: { onChange },
    formState: { errors },
  } = useController({
    name: 'weekly',
    defaultValue: [1],
    rules: {
      required: true,
      validate: (v) => v.length,
    },
  });

  return (
    <Field label={t('日期', 'Date')} align="left">
      <div>
        <DateSelect list={[1, 2, 3, 4, 5, 6, 7]} onChange={onChange} type="weekly" />
        {errors.weekly && (
          <portal.div css={{ textBody: 's', color: 'red.10', marginTop: 4 }}>
            {t('请至少选择一个日期', 'Please select at least one date')}
          </portal.div>
        )}
      </div>
    </Field>
  );
}
