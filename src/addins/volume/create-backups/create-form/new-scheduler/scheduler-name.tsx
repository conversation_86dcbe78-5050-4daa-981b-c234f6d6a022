import React from 'react';
import { t } from '@pitrix/portal-widget';
import { Input, portal } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';

import { Field } from 'common';

export default function SchedulerName(): JSX.Element {
  const {
    field: { value: nameValue, onChange: nameChange },
    formState: { errors },
  } = useController({
    name: 'scheduler_name',
    defaultValue: '',
    rules: { required: true },
  });

  return (
    <Field label={t('定时器名称', 'Scheduler Name')} align="left">
      <div>
        <Input
          value={nameValue}
          onChange={nameChange}
          maxLength={50}
          placeholder={t('请输入定时器名称', 'Please enter the scheduler name')}
        />
        {!!errors.scheduler_name && (
          <portal.div css={{ color: 'red.10', marginTop: 4, lineHeight: '20px' }}>
            {t('请输入定时器名称', 'Please enter the scheduler name')}
          </portal.div>
        )}
      </div>
    </Field>
  );
}
