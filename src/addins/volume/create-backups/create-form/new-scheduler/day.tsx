import React from 'react';
import { InputNumber, portal } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';
import { t } from '@pitrix/portal-widget';

import { Field } from 'common';

export default function Day(): JSX.Element {
  const {
    field: { value, onChange },
  } = useController({
    name: 'every_n_days',
    defaultValue: 1,
  });

  return (
    <Field label={t('天数', 'Days')} align="left">
      <div>
        <InputNumber min={1} max={99} value={value} onChange={onChange} />
        <portal.div css={{ textBody: 's', color: 'text.placeholder', marginTop: 4 }}>
          {t('每 n 天执行一次，其中 n 取值范围 1~99。', 'Execute once every n days with n values ranging from 1 to 99')}
        </portal.div>
      </div>
    </Field>
  );
}
