import React from 'react';
import { portal } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';
import range from 'lodash-es/range';
import { t } from '@pitrix/portal-widget';

import { Field } from 'common';
import DateSelect from './date-select';

export default function Months(): JSX.Element {
  const months = range(1, 32).concat(-1);

  const {
    field: { onChange },
    formState: { errors },
  } = useController({
    name: 'monthly',
    defaultValue: [1],
    rules: {
      required: true,
      validate: (v) => v.length,
    },
  });

  return (
    <Field label={t('日期', 'Date')} align="left">
      <div>
        <DateSelect list={months} onChange={onChange} type="monthly" />
        {errors.monthly && (
          <portal.div css={{ textBody: 's', color: 'red.10', marginTop: 4 }}>
            {t('请至少选择一个日期', 'Please select at least one date')}
          </portal.div>
        )}
      </div>
    </Field>
  );
}
