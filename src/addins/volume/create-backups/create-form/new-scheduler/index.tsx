import React from 'react';
import { useWatch } from 'react-hook-form';

import SchedulerName from './scheduler-name';
import Period from './period';
import Hours from './hour';
import Time from './time';
import Day from './day';
import Week from './week';
import Months from './months';

function NewScheduler(): JSX.Element {
  const period = useWatch({ name: 'period' });
  return (
    <>
      <SchedulerName />
      <Period />
      {period === 'every_n_hours' && <Hours />}
      {period === 'every_n_days' && <Day />}
      {period === 'weekly' && <Week />}
      {period === 'monthly' && <Months />}
      <Time period={period} />
    </>
  );
}

export default NewScheduler;
