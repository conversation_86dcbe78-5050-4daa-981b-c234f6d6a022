import React from 'react';
import { portal } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';
import { t } from '@pitrix/portal-widget';
import { formatHH } from 'utils/dayjs';

import { InputNumber } from 'common/components/input-number';
import { Field } from 'common';

export default function Time({ period }: { period: string }): JSX.Element {
  const {
    field: { value: hValue, onChange: hChange },
  } = useController({
    name: 'hh',
    defaultValue: formatHH('HH'),
  });

  const {
    field: { value: mValue, onChange: mChange },
  } = useController({
    name: 'mm',
    defaultValue: formatHH('mm'),
  });

  return (
    <Field label={t('时间', 'Time')} align="left">
      <portal.div css={{ display: 'flex', alignItems: 'center' }}>
        <InputNumber
          style={{ width: 50 }}
          max={23}
          min={0}
          controls={'' as 'default'}
          disabled={period === 'every_n_hours'}
          value={hValue}
          onChange={hChange}
        />
        <span>&nbsp;&nbsp;:&nbsp;&nbsp;</span>
        <InputNumber
          style={{ width: 50 }}
          max={59}
          min={0}
          controls={'' as 'default'}
          value={mValue}
          onChange={mChange}
        />
      </portal.div>
    </Field>
  );
}
