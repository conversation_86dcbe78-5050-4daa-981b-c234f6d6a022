import React from 'react';
import { portal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';

interface DateSelectPropsType {
  type?: string;
  list: number[];
  onChange: (n: number[]) => void;
}

const activeCss = {
  borderColor: 'brand.active',
  backgroundColor: 'brand.default',
  color: '#fff',
} as const;

const weeks = [
  '',
  t('周一', 'Mon.'),
  t('周二', 'Tues.'),
  t('周三', 'Wed.'),
  t('周四', 'Thur.'),
  t('周五', 'Fri.'),
  t('周六', 'Sat.'),
  t('周日', 'Sun.'),
];

export default function DateSelect({ list = [], onChange, type }: DateSelectPropsType): JSX.Element {
  const [checked, setChecked] = React.useState([1]);

  return (
    <portal.div
      css={{
        display: 'flex',
        flexWrap: 'wrap',
        maxWidth: 360,
        gap: 2,
      }}
    >
      {list.map((day: number) => (
        <portal.span
          css={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'text.base',
            border: 'border.base',
            borderRadius: 's',
            backgroundColor: '#e4ebf1',
            borderColor: '#d6e0e9',
            transition: 'all .3s',
            cursor: 'pointer',
            width: type === 'weekly' ? 45 : 36,
            height: 36,
            textBody: 's',
            _hover: {
              borderColor: 'brand.active',
            },
            ...(checked.includes(day) ? activeCss : {}),
          }}
          key={day}
          onClick={() => {
            const _checked = [...checked];
            const n: number = _checked.indexOf(day);
            if (n < 0) {
              _checked.push(day);
            } else {
              _checked.splice(n, 1);
            }
            setChecked(_checked);
            onChange?.(_checked);
          }}
        >
          {type === 'weekly' && weeks[day]}
          {type !== 'weekly' && day === -1 && t('月末', 'EOM')}
          {type !== 'weekly' && day !== -1 && day}
        </portal.span>
      ))}
    </portal.div>
  );
}
