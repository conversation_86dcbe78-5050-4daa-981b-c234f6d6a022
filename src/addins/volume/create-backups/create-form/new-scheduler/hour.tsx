import React from 'react';
import { InputNumber, portal } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';
import { t } from '@pitrix/portal-widget';

import { Field } from 'common';

export default function Hour(): JSX.Element {
  const {
    field: { value, onChange },
  } = useController({
    name: 'every_n_hours',
    defaultValue: 1,
  });

  return (
    <Field label={t('小时数', 'Hours')} align="left">
      <div>
        <InputNumber min={1} max={23} value={value} onChange={onChange} />
        <portal.div css={{ textBody: 's', color: 'text.placeholder', marginTop: 4 }}>
          {t(
            '每 n 小时执行一次，其中 n 取值范围：1~23。',
            'Execute once every n hours with n values ranging from 1 to 23.',
          )}
        </portal.div>
      </div>
    </Field>
  );
}
