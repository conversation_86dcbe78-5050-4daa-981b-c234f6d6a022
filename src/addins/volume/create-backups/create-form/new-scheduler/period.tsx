import React from 'react';
import { t } from '@pitrix/portal-widget';
import { Select } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';

import { Field } from 'common';

const options = [
  {
    value: 'daily',
    label: t('每天', 'Every day'),
  },
  {
    value: 'every_n_days',
    label: t('每 n 天', 'Every n days'),
  },
  {
    value: 'every_n_hours',
    label: t('每 n 小时', 'Every n hours'),
  },
  {
    value: 'weekly',
    label: t('每周', 'Weekly'),
  },
  {
    value: 'monthly',
    label: t('每月', 'Monthly'),
  },
];

export default function Period(): JSX.Element {
  const {
    field: { value: nameValue, onChange: nameChange },
  } = useController({
    name: 'period',
    defaultValue: 'daily',
  });

  return (
    <Field label={t('周期', 'Period')} align="left">
      <Select value={nameValue} onChange={nameChange} options={options} width={328} />
    </Field>
  );
}
