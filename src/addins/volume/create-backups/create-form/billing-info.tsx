import React from 'react';
import { portal, Alert } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useRequest } from 'common/hooks';

function BillingInfo(): JSX.Element {
  const { data } = useRequest({
    action: 'GetPrice',
    resources: [{ sequence: 0, type: 'snapshot', snapshot_type: 1, size: 1024 }],
    currency: window?.__CURRENCY,
    owner: window.user.user_id,
    zone: window.user.zone,
  });

  const price = data?.price_set?.[0]?.price ?? '--';

  return (
    <Alert
      description={
        <portal.div css={{ color: 'blue.13', lineHeight: '20px' }}>
          <portal.div css={{ marginBottom: 4, fontWeight: 'bold' }}>{t('计费说明', 'Billing Description')}</portal.div>
          <portal.div>
            <span>{t('备份价格', 'Backup Price')}</span>
            <portal.span css={{ margin: '0 6px' }}>=</portal.span>
            <portal.span>
              {t('备份链上所有备份点空间总和 (GB) ', 'Total space of all backup points in the backup chain (GB')}
            </portal.span>
            <portal.span css={{ margin: '0 6px' }}>
              *{window?.__CURRENCY === 'cny' ? '￥' : '$'}
              {price}
            </portal.span>
            <portal.span css={{ margin: '0 6px' }}>{t('每小时', 'every hour')}</portal.span>
            <portal.span css={{ marginLeft: 6, color: 'text.base' }}>
              {t('不足 1GB 按 1GB 计费', 'Less than 1GB will be charged as 1GB')}
            </portal.span>
          </portal.div>
        </portal.div>
      }
      type="info"
      closable={false}
      css={{ marginBottom: 20, width: '100%' }}
    />
  );
}

export default BillingInfo;
