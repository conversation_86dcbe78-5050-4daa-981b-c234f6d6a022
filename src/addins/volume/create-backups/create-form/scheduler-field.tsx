import React, { useMemo, useState } from 'react';
import { t } from '@pitrix/portal-widget';
import { Select, Button, Link, portal } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';

import { Field } from 'common';
import { useInfiniteRequest } from 'common/hooks';
import { getUtcLocalFormat } from 'utils/dayjs';

const Execute = t('执行', 'Execute');

const weeks = [
  '',
  t('周一', 'Mon.'),
  t('周二', 'Tues.'),
  t('周三', 'Wed.'),
  t('周四', 'Thur.'),
  t('周五', 'Fri.'),
  t('周六', 'Sat.'),
  t('周日', 'Sun.'),
];

const getScheldulerDes = (sc: any) => {
  const { period, hhmm } = sc;
  const periodArr = period.split(':');
  const type = periodArr?.[0];
  const date = periodArr?.[1];
  const local_hhmm = getUtcLocalFormat(hhmm, 'HH:mm');
  const mm = local_hhmm.split(':')[1];

  switch (type) {
    case 'daily':
      return `${t('每天', 'Everyday')} ${local_hhmm} ${Execute}`;
    case 'weekly':
      // eslint-disable-next-line no-case-declarations
      const week = date
        ?.split(',')
        .map((d: string | number) => weeks[d as 1])
        .join('、');
      return `${t('每周', 'Weekly')}: ${week} ${local_hhmm} ${Execute}`;
    case 'monthly':
      // eslint-disable-next-line no-case-declarations
      const months = date.split(',');
      // eslint-disable-next-line no-case-declarations, no-nested-ternary
      const sortMonths = months.sort((a: number, b: number) => (+a === -1 ? 1 : +b === -1 ? -1 : +a - +b));
      // eslint-disable-next-line no-case-declarations
      const M = sortMonths
        .map((m: string) => {
          if (+m > 0) {
            return `${m}${t('号', 'rd.')}`;
          }
          return t('月末', '');
        })
        .join('、');
      return `${t('每月', 'Monthly')}: ${M} ${local_hhmm} ${Execute}`;
    case 'every_n_days':
      return `${t(`每 ${date} 天`, `Every ${date} days`)} ${local_hhmm} ${Execute}`;
    case 'every_n_hours':
      return t(`每 ${date} 小时的第 ${mm} 分钟执行`, `Executed every ${date} hours at the ${mm} minute`);
    default:
      return '';
  }
};
function TimerField() {
  const project_id = window?.global_info?.project_id || undefined;
  const { data, hasNextPage, fetchNextPage, refetch, isFetching } = useInfiniteRequest({
    action: 'DescribeSchedulers',
    owner: window.user.user_id,
    zone: window.user.zone,
    limit: 50,
    controller: ['self'],
    project_id,
  });

  const [sTip, setTip] = useState<string>('');

  const options = useMemo(() => {
    const result = data?.pages?.length ? data?.pages.flatMap((item) => item?.scheduler_set ?? []) : [];
    return result
      .map((item) => ({
        label: `${item.scheduler_name || ''}（${item.scheduler_id}）`,
        value: item.scheduler_id,
        ...item,
      }))
      .filter((item) => item.repeat);
  }, [data]);

  const {
    field: { value, onChange },
    formState: { errors },
  } = useController({
    name: 'scheduler',
    shouldUnregister: true,
    rules: {
      required: true,
    },
  });

  return (
    <Field label={t('选择定时器', 'Select Scheduler')} align="left">
      <portal.div css={{ display: 'flex', alignItems: 'flex-start', flexDirection: 'column' }}>
        <portal.div css={{ display: 'flex', alignItems: 'center' }}>
          <Select
            width={328}
            value={value}
            onChange={(v: string, s: any) => {
              onChange(v);
              const tip = getScheldulerDes(s);
              setTip(tip);
            }}
            options={options}
            placeholder={t('请选择定时器', 'Please select a scheduler')}
            showBottomText
            searchable
            loading={isFetching}
            error={errors.scheduler}
            onScrollBottom={() => {
              if (hasNextPage) {
                fetchNextPage();
              }
            }}
          />
          <Button icon="refresh2-fill" onClick={refetch} css={{ margin: '0 12px 0 8px' }} />
        </portal.div>
        {errors.scheduler ? (
          <portal.div css={{ color: 'red.10', lineHeight: '20px', marginTop: 4 }}>
            {t('请选择定时器', 'Please select a scheduler')}
          </portal.div>
        ) : (
          <portal.div css={{ lineHeight: '20px', color: 'text.placeholder', marginTop: 4 }}>
            <portal.div>{sTip}</portal.div>
            <portal.div>
              {t(
                '如您现有的任务不合适，可以选择新增定时器，如需修改已有定时器可前往 ',
                'If your existing task is not suitable, you can choose to add a new timer. If you need to modify the existing timer, you can go to ',
              )}
              <Link target="_blank" href={`/${window.user.zone}/schedulers/`}>
                {t('运维与管理-定时器', 'Operation & Manage - Scheduler')}
              </Link>
              {t(' 中修改。', ' to modify it.')}
            </portal.div>
          </portal.div>
        )}
      </portal.div>
    </Field>
  );
}

export default TimerField;
