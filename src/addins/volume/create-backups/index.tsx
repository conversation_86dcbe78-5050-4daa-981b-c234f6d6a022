import React from 'react';
import { t } from '@pitrix/portal-widget';
import NiceModal from '@ebay/nice-modal-react';
import { useSetAtom } from 'jotai';
import { getDayDate, getUtcHour } from 'utils/dayjs';

import { useMicAppContext, useRequest } from 'common/hooks';
import { BaseModal, request, Volume, Instance } from 'common';
import CreateForm from './create-form';
import { backupInfoAtom } from './state';
import BackupTip from './backup-tips';

interface Props {
  volume?: Volume[];
  instance?: Instance[];
}

export default function CloneVolume(): JSX.Element {
  const { volume, instance, unmountSelf } = useMicAppContext<Props>();
  const setInfo = useSetAtom(backupInfoAtom);
  const project_id = window?.global_info?.project_id || undefined;
  const resources = volume || instance || [];
  const resourcesArr = Array.isArray(resources) ? resources : [resources];
  const supportSnapshot = resourcesArr?.some((item) => (item as Instance)?.is_able_local_snapshot);

  React.useEffect(() => {
    if (resourcesArr.length) {
      setInfo(resourcesArr);
      NiceModal.show('backup-tip');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setInfo]);

  const { data: snapshotData } = useRequest(
    {
      action: 'DescribeSnapshots',
      resource_id: instance?.[0]?.instance_id || volume?.[0]?.volume_id,
      status: ['available'],
      owner: window.user.user_id,
      zone: window.user.zone,
      is_local_snapshot: 1,
    },
    {
      enabled: instance?.length === 1 || volume?.length === 1,
      select: (res) => res?.snapshot_set || [],
    },
  );

  const createSnapshot = async (data: any, close?: () => void) => {
    await request({
      params: {
        action: 'CreateSnapshots',
        snapshot_name: data.snapshot_name,
        resources: data.resources,
        owner: window.user.user_id,
        zone: window.user.zone,
        is_full: data.is_full ? 1 : undefined,
        project_id,
      },
    }).then((res) => {
      if (res?.ret_code === 0) {
        close?.();
        unmountSelf?.();
      }
    });
  };

  const createTask = async (data: any, close?: () => void, id?: string) => {
    await request({
      params: {
        action: 'AddSchedulerTasks',
        scheduler: data.scheduler || id,
        scheduler_tasks: [
          {
            scheduler_task_name: data.scheduler_task_name,
            task_type: 'create_snapshots',
            chain_len: data.chain_len,
            auto_delete: data.auto_delete,
            chain_limit: data.chain_limit,
            task_params: {
              resources: data.resources,
              chain_len: data.chain_len,
              auto_delete: data.auto_delete,
              chain_limit: +data.auto_delete ? data.chain_limit : undefined,
            },
          },
        ],
        owner: window.user.user_id,
        zone: window.user.zone,
        project_id,
      },
    }).then((res) => {
      if (res?.ret_code === 0) {
        close?.();
        unmountSelf?.();
      }
    });
  };
  const createScheduler = async (data: any, close?: () => void) => {
    const { scheduler_name, hh, mm, period } = data;
    // 将小时转化为UTC时间
    const utcDate = getUtcHour(hh);
    const h = utcDate.get('hour');
    const hhmm = `${h}:${mm}`;

    let periodData = data?.[period];

    // 如果当前UTC时间为前一天的时间，则星期回退一天
    if (period === 'weekly' && utcDate.get('date') !== getDayDate()) {
      periodData = periodData?.map((day: number) => {
        const weeks = [0, 7, 1, 2, 3, 4, 5, 6];
        return weeks?.[day];
      });
    }

    if (period === 'weekly' || period === 'monthly') {
      periodData = periodData?.join(',');
    }

    const params = {
      scheduler_name,
      hhmm,
      period: periodData !== undefined ? `${period}:${periodData}` : period,
      notification_lists: [],
      zone: window.user.zone,
      owner: window.user.user_id,
      project_id,
    };

    const result = await request({
      params: {
        action: 'CreateScheduler',
        repeat: 1,
        ...params,
      },
    });

    if (result?.ret_code === 0 && result?.scheduler_id) {
      const scheduler_id = result.scheduler_id as string;
      await createTask(data, close, scheduler_id);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSubmit = async (data: any, close?: () => void) => {
    const { auto_backup, new_scheduler } = data;
    // 手动备份
    if (+auto_backup) {
      if (+new_scheduler) {
        await createScheduler(data, close);
      } else {
        await createTask(data, close);
      }
    } else {
      await createSnapshot(data, close);
    }
  };

  return (
    <>
      <BaseModal
        id="backup-tip"
        name="backup-tip"
        title={t('提示', 'Prompt')}
        width={600}
        okText={t('继续', 'Continue')}
        onOk={() => {
          NiceModal.show('create-backups');
        }}
        onCancel={() => {
          unmountSelf?.();
        }}
      >
        <BackupTip hasSnapshot={snapshotData?.length > 0 && !!instance && !instance?.[0].extra?.tps_vg} />
      </BaseModal>
      <BaseModal
        id="create-backups"
        name="create-backups"
        width={600}
        title={t('创建备份', 'Creating Backups')}
        okText={t('提交', 'Submit')}
        onCancel={() => {
          unmountSelf?.();
        }}
        onAsyncOk={handleSubmit}
      >
        <CreateForm supportSnapshot={supportSnapshot} />
      </BaseModal>
    </>
  );
}
