import React from 'react';
import { t } from '@pitrix/portal-widget';
import { Alert } from '@pitrix/portal-ui';

function BackupTips({ hasSnapshot }: { hasSnapshot: boolean }) {
  return (
    <Alert
      closable={false}
      type="warning"
      title={t(
        '当你对正在运行的云服务器或者已经绑定的硬盘做在线备份时，需要注意以下几点：',
        'When you perform an online backup on a running cloud server or a bound disk, you need to pay attention to the following points:',
      )}
      description={
        <>
          <div>
            1.
            {t(
              '备份只能捕获在备份任务开始时已经写入磁盘的数据，不包括当时位于缓存里的数据。',
              'The backup can only capture the data that has been written to the disk at the time the backup task starts, and does not include the data that is currently in the cache.',
            )}
          </div>
          <div>
            2.
            {t(
              '为了保证数据的完整性，你需要在创建备份前暂停所有文件的写操作，直到备份进入"捕获完成"的状态。或者先停止云服务器或解绑硬盘，进行离线备份。',
              'To ensure the integrity of the data, you need to pause all file write operations before creating the backup until the backup enters the "Capture Complete" state. Or stop the cloud server or unbind the disk first, and then perform offline backup.',
            )}
          </div>
          {hasSnapshot && (
            <div>
              3.
              {t(
                '首次备份完成后会生成一条“BACKUP-SNAPSHOT_ss”的系统快照，备份时会删除所有已存在的快照数据。',
                'The first backup will generate a system snapshot named "BACKUP-SNAPSHOT_ss", and all existing snapshot data will be deleted during backup.',
              )}
            </div>
          )}
        </>
      }
    />
  );
}

export default BackupTips;
