import React from 'react';

import { useMicAppContext } from 'common/hooks';
import CreateVolume from '../../../pages/volume/create/forms/default-form';
import { CreateVolumeProps } from '../../../pages/volume/interfaces';

export default function CreateVolumeWidthVdc(): JSX.Element {
  const { title, onSubmit, region, onBackup, noPrice, projectId, submitLoading, submitButtonText } =
    useMicAppContext<CreateVolumeProps>();

  return (
    <CreateVolume
      title={title}
      noPrice={noPrice}
      onSubmit={onSubmit}
      region={region}
      onBackup={onBackup}
      projectId={projectId}
      submitLoading={submitLoading}
      submitButtonText={submitButtonText}
    />
  );
}
