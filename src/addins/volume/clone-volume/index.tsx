import React from 'react';
import { t, Volume } from '@pitrix/portal-widget';
import NiceModal from '@ebay/nice-modal-react';

import { useMicAppContext } from 'common/hooks';
import { BaseModal, request } from 'common';
import CloneVolumeForm from './clone-form';

interface CloneVolumeProps {
  volume: Volume;
}

export default function CloneVolume(): JSX.Element {
  const { volume, unmountSelf } = useMicAppContext<CloneVolumeProps>();

  React.useEffect(() => {
    if (volume) {
      NiceModal.show('clone-volume-modal');
    }
  }, [volume]);

  return (
    <BaseModal
      id="clone-volume-modal"
      name="clone-volume-modal"
      width={600}
      title={t('克隆硬盘', 'Clone volume')}
      okText={t('提交', 'Submit')}
      onCancel={() => {
        unmountSelf?.();
      }}
      onAsyncOk={async (data, close) => {
        await request({
          params: {
            action: 'CloneVolumes',
            ...data,
            zone: data?.sub_zones,
            owner: window?.user?.user_id,
            volume: volume?.volume_id,
            project_id: window?.global_info?.project_id || undefined,
          },
        }).then((res) => {
          if (res?.ret_code === 0) {
            close?.();
            unmountSelf?.();
          }
        });
      }}
    >
      <CloneVolumeForm volume={volume} />
    </BaseModal>
  );
}
