import React from 'react';
import { portal, Input, Select, RadioGroup, Alert } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';
import {
  t,
  Volume,
  useGetVolumePriceQuery,
  useDescribeVolumeTypesQuery,
  DescribeVolumeTypesRequest,
  GetVolumePriceRequest,
  currencyText,
} from '@pitrix/portal-widget';
import get from 'lodash-es/get';
import BigNumber from 'bignumber.js';

import { InputNumber } from 'common/components/input-number';
import { Field, PlgSelect } from 'common';
import { Conf } from 'utils';
import { useZoneConfig } from 'common/hooks';
import { isSupportPlg } from 'pages/volume/utils';

interface CloneVolumeFormProps {
  volume?: Volume;
}

export default function CloneVolumeForm({ volume }: CloneVolumeFormProps): JSX.Element {
  const [plgCount, setPlgCount] = React.useState(false);

  const zone = window?.user?.zone as string;
  const owner = window?.user?.user_id;
  const zoneInfo = get(window?.user?.region_info, zone);
  const supportBilling = !!window?.GLOBAL_CONFIG?.SYSTEM_SETTINGS?.support_billing;

  const subZones = get(zoneInfo, 'zones', []);
  const defaultZone = volume?.zone_id || get(zoneInfo, 'default_zone', subZones?.[0]);

  // 表单项名称
  const {
    field: { value: nameValue, onChange: nameChange },
  } = useController({
    name: 'volume_name',
    defaultValue: '',
  });
  // 表单项拷贝数量
  const {
    field: { value: countValue, onChange: countChange },
  } = useController({
    name: 'count',
    defaultValue: 1,
  });
  // 表单项可用去
  const {
    field: { value: zoneValue, onChange: zoneChange },
  } = useController({
    name: 'sub_zones',
    defaultValue: defaultZone,
  });
  // 表单项资源池
  const {
    field: { value: plgValue, onChange: plgChange },
  } = useController({
    name: 'place_group_id',
  });
  // 表单项克隆硬盘类型
  const {
    field: { value: volumeTypeValue, onChange: volumeTypeChange },
    formState: { errors },
  } = useController({
    name: 'volume_type',
    rules: { required: true },
  });

  const { volume_type, size } = volume as Volume;
  const userZone = React.useMemo(() => {
    if (!Conf.isRegion()) {
      return volume?.zone_id;
    }
    return zoneValue || volume?.zone_id;
  }, [zoneValue, volume]);
  const zoneConfig = useZoneConfig(userZone);

  const cloneMap = get(zoneConfig, 'clone_volume_type_map', {});

  const supportCloneType = get(cloneMap, `volume_type_${volume_type}`, []) as number[];

  const _volumeType = React.useMemo(() => {
    return volumeTypeValue !== undefined ? volumeTypeValue : volume_type;
  }, [volume_type, volumeTypeValue]);

  const { data: volumeTypes, isLoading } = useDescribeVolumeTypesQuery(
    {
      zone: volume?.zone_id,
      owner,
      limit: 100,
      status: ['available'],
      offset: 0,
      project_id: window?.global_info?.project_id || undefined,
      place_group_id: plgValue || undefined,
    } as DescribeVolumeTypesRequest,
    {
      enabled: !!volume?.zone_id,
    },
  );

  const { data: priceData } = useGetVolumePriceQuery(
    {
      currency: window.__CURRENCY || 'cny',
      zone: volume?.zone_id,
      owner,
      resources: [
        {
          duration: 3600,
          type: 'volume',
          sequence: 0,
          size,
          volume_type: _volumeType,
        },
        {
          duration: 1,
          type: 'volume',
          sequence: 1,
          size,
          volume_type: _volumeType,
        },
      ],
    } as unknown as GetVolumePriceRequest,
    {
      enabled: !!_volumeType !== undefined && !!zone && !!size && supportBilling,
    },
  );

  const zoneOptions = () => {
    const zones = window?.user?.zones_info ?? [];
    const lang = window?.user?.lang ?? 'zh-cn';
    return subZones?.map((_zone: string) => {
      if (zones?.[_zone]) {
        return {
          label: get(zones?.[_zone], lang, _zone),
          value: _zone,
        };
      }
      return {
        label: _zone,
        value: _zone,
      };
    });
  };

  const [hourPrice, monthPrice] = React.useMemo(() => {
    let _hourPrice;
    let _monthPrice = 0;
    if (priceData?.price_set) {
      const price0 = priceData?.price_set?.find((info) => info?.sequence === 0)?.price ?? 0;
      _hourPrice = new BigNumber(price0).times(countValue).toNumber();
      const price1 = priceData?.price_set?.find((info) => info?.sequence === 1)?.price ?? 0;
      _monthPrice = new BigNumber(price1).times(countValue).toNumber();
    }
    return [_hourPrice, _monthPrice];
  }, [priceData, countValue]);

  const volumeOptions = React.useMemo(() => {
    const types = volumeTypes?.instance_type_set ?? [];
    return types
      .filter((_volume) => supportCloneType?.includes?.(_volume?.resource_class))
      .map((_volume) => ({
        ..._volume,
        label: window.p_text(`volume.attrs.volume_type.${_volume?.resource_class}`) || _volume?.instance_type_name,
        value: String(_volume?.resource_class),
        description: '',
      }));
  }, [volumeTypes, supportCloneType]);

  React.useEffect(() => {
    if (volumeOptions?.length) {
      const isTypeInOption = volumeOptions.find((item) => +(item?.value ?? -1) === +volume_type);
      if (isTypeInOption) {
        volumeTypeChange(String(volume_type));
      } else {
        volumeTypeChange(undefined);
      }
    } else {
      volumeTypeChange(undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [volumeOptions, volume_type]);

  const labelWidth = window.user.lang === 'en' ? 110 : 72;
  return (
    <portal.div>
      {supportBilling && (
        <Alert
          description={
            <portal.div css={{ color: 'blue.13' }}>
              <portal.div css={{ marginBottom: 15 }}>
                {t(
                  '您可以将硬盘创建一份或多份拷贝，新拷贝创建后按相应类型和容量费用收取。',
                  'You can create one or more copies of the hard drive, and charge a corresponding type and capacity fee after creating a new copy.',
                )}
              </portal.div>
              {t('合计:', 'Total:')}
              <portal.span css={{ fontWeight: 'bold', color: 'brand.default' }}>
                {' '}
                {currencyText()}
                {hourPrice}{' '}
              </portal.span>
              {t('每小时（合', 'every hour (Combined)')}
              <portal.span css={{ fontWeight: 'bold', color: 'brand.default' }}>
                {' '}
                {currencyText()}
                {monthPrice}{' '}
              </portal.span>
              {t('每月）', 'month)')}
            </portal.div>
          }
          type="info"
          closable={false}
          css={{ marginBottom: 20, width: '100%' }}
        />
      )}
      <Field labelWidth={labelWidth} label={t('名称', 'Name')}>
        <Input value={nameValue} onChange={nameChange} />
      </Field>
      <Field labelWidth={labelWidth} label={t('拷贝数量', 'Number of Copies')}>
        <InputNumber min={1} max={100} value={countValue} onChange={countChange} />
      </Field>
      {Conf.isRegion() && (
        <Field labelWidth={labelWidth} label={t('可用区', 'Zone')}>
          <Select css={{ width: 328 }} value={zoneValue} onChange={zoneChange} options={zoneOptions()} />
        </Field>
      )}
      {isSupportPlg() && (
        <Field
          labelWidth={labelWidth}
          label={t('资源池', 'Resource Pool')}
          css={{ display: plgCount ? 'inline-flex' : 'none' }}
          contentWidth={328}
        >
          <PlgSelect
            resourceType="volume"
            zone={userZone}
            value={plgValue}
            needSystemGrant={false}
            onChange={(id, _obj, haveLen?: boolean) => {
              plgChange(id);
              setPlgCount(!!haveLen);
            }}
          />
        </Field>
      )}
      <Field labelWidth={labelWidth} label={t('类型', 'Type')}>
        {isLoading && <portal.div css={{ color: 'text.placeholder' }}>{t('加载中...', 'Loading...')}</portal.div>}
        {!isLoading && !!volumeOptions?.length && (
          <RadioGroup value={volumeTypeValue} onChange={volumeTypeChange} options={volumeOptions} />
        )}
        {!isLoading && !volumeOptions?.length && (
          <portal.div
            css={{
              color: errors?.volume_type ? 'red.10' : 'text.placeholder',
            }}
          >
            {t('暂无可选类型', 'There are currently no optional types available')}
          </portal.div>
        )}
      </Field>
    </portal.div>
  );
}
