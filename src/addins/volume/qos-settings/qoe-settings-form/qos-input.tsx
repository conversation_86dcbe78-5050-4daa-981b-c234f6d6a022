import React from 'react';
import { InputNumber, portal } from '@pitrix/portal-ui';
import { useFormatQos } from 'common/hooks';
import { useController, useFormContext } from 'react-hook-form';
import get from 'lodash-es/get';
import { t } from '@pitrix/portal-widget';
import { TooltipSelf } from 'common';

interface QosInputProps {
  label?: string;
  showTypeName?: boolean;
  ioName?: string;
  iopsName?: string;
  volumeSize?: number;
  resourceQos?: string;
  defaultIo?: number;
  defaultIops?: number;
}

export default function QosInput({
  label,
  showTypeName,
  ioName,
  iopsName,
  volumeSize,
  resourceQos,
  defaultIo,
  defaultIops,
}: QosInputProps): JSX.Element {
  const { IO, IOPS, IO_TIP, IOPS_TIP } = useFormatQos({
    qos: resourceQos,
    size: volumeSize,
  });
  const { control } = useFormContext();

  const {
    field: { onChange: iopsChange, value: iops },
    formState: { errors },
  } = useController({
    name: iopsName || 'iops',
    control,
    defaultValue: defaultIops || IOPS,
    rules: {
      required: true,
      min: 1,
    },
  });

  const {
    field: { onChange: ioChange, value: io },
    formState: { errors: ioErrors },
  } = useController({
    name: ioName || 'throughput',
    defaultValue: defaultIo || IO,
    control,
    rules: {
      required: true,
      min: 1,
    },
  });

  return (
    <portal.div css={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
      <TooltipSelf content={label} needTooltip={!!label} zIndex={104}>
        <portal.div
          title={label}
          css={{
            position: 'relative',
            textBody: 's',
            width: 100,
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            marginRight: 10,
          }}
        >
          {showTypeName && <portal.div>{t('数据盘名称:', 'Data disk name:')}</portal.div>}
          <portal.div css={{ color: !label && 'text.placeholder' }}>{label || t('未命名', 'Unnamed')}</portal.div>
        </portal.div>
      </TooltipSelf>
      <TooltipSelf content={IO_TIP} needTooltip={!!IO_TIP} zIndex={104}>
        <div>
          <InputNumber
            controls={'' as unknown as 'default'}
            unit="MB/s"
            value={io}
            onChange={ioChange}
            error={ioName ? !!get(ioErrors, ioName) : !!ioErrors.throughput}
          />
        </div>
      </TooltipSelf>
      <TooltipSelf content={IOPS_TIP} needTooltip={!!IOPS_TIP} zIndex={104}>
        <div>
          <InputNumber
            controls={'' as unknown as 'default'}
            style={{ marginLeft: 20 }}
            value={iops}
            onChange={iopsChange}
            error={iopsName ? !!get(errors, iopsName) : !!errors.iops}
          />
        </div>
      </TooltipSelf>
    </portal.div>
  );
}
