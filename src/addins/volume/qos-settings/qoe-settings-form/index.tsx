import React from 'react';
import { portal } from '@pitrix/portal-ui';
import {
  t,
  Instance,
  Volume,
  useDescribeVolumeTypesQuery,
  DescribeVolumeTypesRequest,
  VolumeType,
} from '@pitrix/portal-widget';
import { useFormContext, useFieldArray } from 'react-hook-form';
import get from 'lodash-es/get';

import { useOsDiskType } from 'common/hooks';
import QosInput from './qos-input';

interface QosSettingFormProps {
  model?: Instance | Volume;
}

interface FieldsType {
  id?: string;
  iops?: number;
  throughput?: number;
  defaultIo?: number;
  defaultIops?: number;
  volume_type?: number;
  size?: number;
  volume_name?: string;
  volume_id?: string;
}

type VolumeTypesObj = Record<string, VolumeType>;

export default function QosSettingForm({ model }: QosSettingFormProps): JSX.Element {
  const zone = window?.user?.zone;
  const { control } = useFormContext();

  const { fields, append } = useFieldArray({
    control,
    name: 'qos_dict',
  });

  const instanceClass = get(model, 'instance_class');
  const instanceId = get(model, 'instance_id', '');
  const volumeName = get(model, 'volume_name', '-');
  const volumeSize = get(model, 'extra.os_disk_size', 0) || get(model, 'size', 0);

  // 系统盘类型
  const osDiskType = useOsDiskType(instanceClass);
  // 当前硬盘类型
  const volumeType = get(model, 'volume_type');

  const labelName = instanceId ? t('系统盘', 'system disk') : volumeName;

  const { data } = useDescribeVolumeTypesQuery(
    {
      zone,
      limit: 100,
      status: ['available'],
      offset: 0,
    } as DescribeVolumeTypesRequest,
    {
      enabled: !!zone,
    },
  );

  // 所有硬盘类型扁平化数据
  const allVolumeTypes = React.useMemo(() => {
    const datas = data?.instance_type_set ?? [];
    return datas.reduce((typesObj: VolumeTypesObj, cur: VolumeType) => {
      const type = cur?.resource_class;
      typesObj[`volume_${type}`] = cur;
      return typesObj;
    }, {});
  }, [data?.instance_type_set]);

  const getResourceQos = React.useCallback(
    (type: number) => {
      return get(allVolumeTypes, [`volume_${type}`, 'resource_qos'], '');
    },
    [allVolumeTypes],
  );

  // 当前硬盘qos
  const resourceQos = React.useMemo(() => {
    if (osDiskType !== undefined) {
      return getResourceQos(osDiskType);
    }
    if (volumeType !== undefined) {
      return getResourceQos(volumeType);
    }
    return '';
  }, [osDiskType, volumeType, getResourceQos]);

  function convertThroughput(throughput: number): number {
    if (throughput) {
      return +(+throughput / 1024).toFixed(0);
    }
    return 0;
  }

  const [defaultIo, defaultIops] = React.useMemo(() => {
    let _throughput: number = 0,
      _iops: number = 0;
    if (model) {
      // 主机入口
      if ('instance_id' in model) {
        // 系统盘默认iops/throughput
        _iops = get(model, 'extra.iops', 0);
        _throughput = get(model, 'extra.throughput', 0);
      } else {
        _iops = get(model, 'iops', 0);
        _throughput = get(model, 'throughput', 0);
      }
      _throughput = convertThroughput(_throughput);
    }
    return [_throughput, _iops];
  }, [model]);
  // 初始化数据
  React.useEffect(() => {
    if (model) {
      let _volumes: Instance['volumes'] = [];
      // 主机入口
      if ('instance_id' in model) {
        _volumes = get(model, 'volumes', []);
      }

      // 如果主机有绑定的数据盘,则赋值给fields
      if (_volumes?.length) {
        _volumes.forEach((item: FieldsType) => {
          append({
            ...item,
            iops: item?.iops,
            throughput: convertThroughput(item?.throughput ?? 0),
          });
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [model, append]);

  return (
    <portal.div>
      <portal.div
        css={{
          display: 'flex',
          color: 'text.placeholder',
          lineHeight: '20px',
          paddingLeft: 108,
          marginBottom: 4,
        }}
      >
        <portal.span>{t('IO吞吐', 'IO throughput')}</portal.span>
        <portal.span css={{ marginLeft: 125 }}>IOPS</portal.span>
      </portal.div>
      <QosInput
        label={instanceId ? t('系统盘', 'system disk') : volumeName}
        defaultIops={defaultIops}
        defaultIo={defaultIo}
        resourceQos={resourceQos}
        volumeSize={volumeSize}
      />
      {!!fields?.length && (
        <portal.div
          css={{
            width: '100%',
            height: 1,
            backgroundColor: 'bg.deep',
            marginBottom: 8,
          }}
        />
      )}

      {fields?.map((field: FieldsType, index) => (
        <QosInput
          showTypeName
          key={field.id}
          ioName={`qos_dict.${index}.throughput`}
          iopsName={`qos_dict.${index}.iops`}
          label={field?.volume_name ?? ''}
          resourceQos={getResourceQos(field.volume_type ?? 0)}
          volumeSize={field?.size}
        />
      ))}
    </portal.div>
  );
}
