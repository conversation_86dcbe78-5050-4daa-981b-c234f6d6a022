import React from 'react';
import { t, Instance, Volume } from '@pitrix/portal-widget';
import NiceModal from '@ebay/nice-modal-react';

import { useMicAppContext } from 'common/hooks';
import { BaseModal, request } from 'common';
import QosSettingForm from './qoe-settings-form';

interface CloneVolumeProps {
  model: Volume | Instance;
  onsuccess: () => void;
}
export default function QusSettings(): JSX.Element {
  const { model, unmountSelf, onsuccess } = useMicAppContext<CloneVolumeProps>();

  React.useEffect(() => {
    if (model) {
      NiceModal.show('qos-settings-modal');
    }
  }, [model]);

  return (
    <NiceModal.Provider>
      <BaseModal
        id="qos-settings-modal"
        name="qos-settings-modal"
        width={450}
        title={t('Qos设置', 'Qos settings')}
        onCancel={() => {
          unmountSelf?.();
        }}
        onAsyncOk={async (data, close) => {
          let dict = undefined;
          const params = {
            iops: data?.iops,
            throughput: data?.throughput * 1024,
          };
          if (data?.qos_dict?.length) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            dict = data.qos_dict.reduce((result: any, item: any) => {
              return {
                ...result,
                [item?.volume_id]: {
                  iops: item?.iops,
                  throughput: item?.throughput * 1024,
                },
              };
            }, {});
            // 主机有数据盘时，将系统盘也qos也加入到参数qos_dict中
            if (model && 'instance_id' in model) {
              dict[model.instance_id] = params;
            }
          }

          const resource =
            model && 'instance_id' in model ? { instances: [model.instance_id] } : { volumes: [model?.volume_id] };

          await request({
            params: {
              action: 'ChangeVolumeQos',
              zone: window?.user?.zone,
              owner: window?.user?.user_id,
              qos_dict: dict && JSON.stringify(dict),
              ...resource,
              ...params,
            },
          }).then((res) => {
            if (res?.ret_code === 0) {
              close?.();
              unmountSelf?.();
              onsuccess?.();
            }
          });
        }}
      >
        <QosSettingForm model={model} />
      </BaseModal>
    </NiceModal.Provider>
  );
}
