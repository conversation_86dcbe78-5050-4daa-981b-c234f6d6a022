import React, { ComponentProps, useCallback } from 'react';
import { ResizeInstancesResponse } from '@pitrix/portal-widget';

import { useMicAppContext } from 'common/hooks';
import { ChangeConfiguration } from 'pages/instance/list/modals';

const ChangeConfigurationQiankun = () => {
  const { instanceId, onCancel, onOk, unmountSelf } = useMicAppContext<ComponentProps<typeof ChangeConfiguration>>();

  const onSuccessCb = useCallback(
    (res: ResizeInstancesResponse) => {
      onOk?.(res);
      unmountSelf?.();
    },
    [onOk, unmountSelf],
  );

  const onCancelCb = useCallback(() => {
    onCancel?.();
    unmountSelf?.();
  }, [onCancel, unmountSelf]);

  if (instanceId) {
    return <ChangeConfiguration onOk={onSuccessCb} instanceId={instanceId} onCancel={onCancelCb} />;
  }

  return null;
};

export default ChangeConfigurationQiankun;
