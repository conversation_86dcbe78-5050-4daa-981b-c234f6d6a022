import React from 'react';
import NiceModal from '@ebay/nice-modal-react';

import { useMicAppContext } from 'common/hooks';
import CreateSnapshotModal from 'pages/snapshot/modals/create-snapshot';
import { Instance } from 'common/types';

interface MicAppProps {
  instance: Instance;
}

NiceModal.register('create-snapshot-modal', CreateSnapshotModal);

export default function CreateSnapshot(): JSX.Element {
  const { instance, unmountSelf } = useMicAppContext<MicAppProps>();

  React.useEffect(() => {
    if (instance) {
      NiceModal.show('create-snapshot-modal', { snapshot: { resource_id: instance?.instance_id }, unmountSelf });
    }
  }, [instance, unmountSelf]);

  return <div />;
}
