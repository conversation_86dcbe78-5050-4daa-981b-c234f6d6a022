import React, { useEffect } from 'react';
import { t } from '@pitrix/portal-widget';
import { get } from 'lodash-es';
import NiceModal from '@ebay/nice-modal-react';

import { request, BaseModal, ConfirmContent } from 'common';
import { useMicAppContext, useRequest } from 'common/hooks';

interface QiankunProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  instance: any;
}

// const getRoot = () => document.getElementById('iaas-addins-container');
const ID = 'cancel-scheduled-deletion';
function CancelScheduledDeletion() {
  const { instance, onOk, unmountSelf } = useMicAppContext<QiankunProps>();

  const { data } = useRequest(
    {
      action: 'DescribeResourceSchedulerTasks',
      zone: window.user.zone,
      resource_type: 'instance',
      resources: [instance?.instance_id],
      task_type: 'terminate_instances',
      controller: 'instance',
      verbose: 1,
    },
    {
      enabled: !!instance?.instance_id,
    },
  );

  const onSubmit = async () => {
    const dataSet = data?.resource_scheduler_tasks;
    const schedulerId = get(dataSet?.[instance?.instance_id], [0, 'scheduler_id'], '');
    if (!schedulerId) {
      onOk?.();
      unmountSelf?.();
      return;
    }
    await request({
      params: {
        action: 'DeleteSchedulers',
        schedulers: [schedulerId],
        owner: window.user.user_id,
        zone: window?.user?.zone,
      },
    }).then((res) => {
      if (res?.ret_code === 0) {
        onOk?.();
        unmountSelf?.();
      }
    });
  };

  useEffect(() => {
    NiceModal.show(ID);
  }, []);

  return (
    <BaseModal
      width={400}
      id={ID}
      name={ID}
      title=""
      okType="primary"
      onAsyncOk={onSubmit}
      onCancel={() => {
        unmountSelf?.();
      }}
      bordered={false}
    >
      <ConfirmContent
        type="info"
        title={t('确定要撤销定时删除？', 'are you sure you want to undo scheduled deletion?')}
      />
    </BaseModal>
  );
}

export default CancelScheduledDeletion;
