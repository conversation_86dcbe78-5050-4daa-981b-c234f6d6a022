import React from 'react';
import NiceModal from '@ebay/nice-modal-react';
import { Image, Instance } from '@pitrix/portal-widget';

import { request } from 'common';
import { useMicAppContext } from 'common/hooks';
import SelectImages from 'pages/instance/list/modals/select-images';

interface MicAppProps {
  instance: Instance & { mount_status: boolean };
}

export default function IsoManage(): JSX.Element {
  const { instance, unmountSelf, cb } = useMicAppContext<MicAppProps>();

  const handleSubmit = async (image: Image) => {
    const res = await request({
      params: {
        action: 'AttachImage',
        instance: instance?.instance_id,
        image: image.image_id,
        zone: instance?.zone_id || window?.user?.zone,
        owner: window?.user?.user_id,
      },
    });
    if (res && res.ret_code === 0) {
      cb?.(res);
      unmountSelf?.();
    }
  };

  React.useEffect(() => {
    if (instance) {
      NiceModal.show('iso-manage-modal');
    }
  }, [instance]);

  return (
    <SelectImages
      id="iso-manage-modal"
      instance={instance}
      onSubmit={handleSubmit}
      onCancel={() => {
        unmountSelf?.();
      }}
    />
  );
}
