import React, { useEffect, useMemo } from 'react';
import { t } from '@pitrix/portal-widget';
import { useAtomValue, useSetAtom } from 'jotai';
import dayjs from 'dayjs';
import { get } from 'lodash-es';
import utc from 'dayjs/plugin/utc';

import { useMicAppContext, useRequest } from 'common/hooks';
import { BaseModal } from 'common';
import ModalContent from './modal-content';
import {
  instanceAtom,
  schedulerInfoAtom,
  deleteTypeAtom,
  schedulerInstanceIdsAtom,
  deleteEipAtom,
  deleteVolumeAtom,
} from './state';

dayjs.extend(utc);

interface SchedulerTask {
  scheduler: {
    scheduler_id: string;
    ymd: string;
    hhmm: string;
  };
}

export default function ScheduledDeletion(): JSX.Element {
  const { unmountSelf, onClose } = useMicAppContext();
  const instance = useAtomValue(instanceAtom);
  const setSchedulerInfo = useSetAtom(schedulerInfoAtom);
  const setDeletionType = useSetAtom(deleteTypeAtom);
  const setShedulerInstanceIds = useSetAtom(schedulerInstanceIdsAtom);
  const setDeleteEip = useSetAtom(deleteEipAtom);
  const setDeleteVolume = useSetAtom(deleteVolumeAtom);

  // 使用 useMemo 缓存实例 ID 列表
  const instanceIds = useMemo(() => {
    return instance?.map((item) => item?.instance_id).filter(Boolean);
  }, [instance]);

  // 查询定时删除任务
  const { data } = useRequest(
    {
      action: 'DescribeResourceSchedulerTasks',
      zone: window.user.zone,
      resource_type: 'instance',
      resources: instanceIds,
      task_type: 'terminate_instances',
      controller: 'instance',
      verbose: 2,
    },
    {
      enabled: instanceIds?.length > 0,
      select: (_data) => _data?.resource_scheduler_tasks,
    },
  );

  // 处理单个实例的定时删除信息
  const handleSingleInstance = useMemo(() => {
    if (instanceIds?.length !== 1 || !data) return null;

    const instanceId = instanceIds[0];
    const curScheduler = get(data, [instanceId, 0, 'scheduler']);

    if (!curScheduler) return null;

    const { scheduler_id, ymd, hhmm } = curScheduler;
    const localTime = dayjs.utc(`${ymd} ${hhmm}`).local();

    return {
      scheduler: scheduler_id,
      ymd: localTime.format('YYYY-MM-DD'),
      hhmm: localTime.format('HH:mm'),
    };
  }, [instanceIds, data]);

  // 处理批量实例的定时删除信息
  const { allSchedulerIds, allInstanceIds } = useMemo(() => {
    if (!data) return { allSchedulerIds: [], allInstanceIds: [] };

    const insIds = Object.keys(data);
    const schedulerIds = Object.values(data).flatMap((arr: SchedulerTask[]) => {
      return arr?.map((item) => item?.scheduler?.scheduler_id).filter(Boolean);
    });

    return {
      allSchedulerIds: schedulerIds,
      allInstanceIds: insIds,
    };
  }, [data]);

  // 重置状态
  useEffect(() => {
    return () => {
      setSchedulerInfo({});
    };
  }, [setSchedulerInfo]);

  // 初始化删除资源
  useEffect(() => {
    setDeleteEip(false);
    setDeleteVolume(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 更新调度信息
  useEffect(() => {
    if (handleSingleInstance) {
      setSchedulerInfo(handleSingleInstance);
      setDeletionType('1');
    } else {
      setSchedulerInfo({});
      setDeletionType('0');
    }

    setShedulerInstanceIds([allInstanceIds, allSchedulerIds]);
  }, [
    handleSingleInstance,
    allInstanceIds,
    allSchedulerIds,
    setSchedulerInfo,
    setDeletionType,
    setShedulerInstanceIds,
  ]);

  return (
    <BaseModal
      id="deletion-instance-modal"
      name="deletion-instance-modal"
      width={600}
      title={t('删除', 'Delete')}
      onCancel={() => {
        unmountSelf?.();
        onClose?.();
      }}
      noFooter
    >
      <ModalContent />
    </BaseModal>
  );
}
