import { atom } from 'jotai';
import { Instance } from 'common';

interface SchedulerInfo {
  scheduler?: string;
  ymd?: string;
  hhmm?: string;
}

interface TableDate {
  id: string;
  name: string;
  father: string;
  type: string;
  typeName: string;
}

export const stepAtom = atom(0);

export const instanceAtom = atom<Instance[]>([]);

export const schedulerInfoAtom = atom<SchedulerInfo>({});

export const schedulerInstanceIdsAtom = atom<string[][]>([[], []]);

export const deleteTypeAtom = atom('0');

export const deleteTimeAtom = atom('');

export const deleteVolumeAtom = atom(false);

export const deleteEipAtom = atom(false);

export const notifyUserAtom = atom<string[] | undefined>();

export const delResourcesAtom = atom<TableDate[]>([]);
