import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Button, portal, Checkbox, Message } from '@pitrix/portal-ui';
import { useAtom, useAtomValue } from 'jotai';
import { t } from '@pitrix/portal-widget';
import dayjs from 'dayjs';

import { IaasModal } from 'common';
import { validateDeletionTime } from 'common/utils';
import { useSubmit, useMicAppContext } from 'common/hooks';
import {
  stepAtom,
  deleteTimeAtom,
  deleteTypeAtom,
  instanceAtom,
  delResourcesAtom,
  schedulerInfoAtom,
  notifyUserAtom,
  schedulerInstanceIdsAtom,
} from './state';

function ModalFooter(): JSX.Element {
  const { unmountSelf, cb } = useMicAppContext();
  const [step, setStep] = useAtom(stepAtom);
  const [read, setRead] = useState(false);
  const type = useAtomValue(deleteTypeAtom);
  const [time, setTime] = useAtom(deleteTimeAtom);
  const instance = useAtomValue(instanceAtom);
  const delResources = useAtomValue(delResourcesAtom);
  const schedulerInfo = useAtomValue(schedulerInfoAtom);
  const notifyUser = useAtomValue(notifyUserAtom);
  const schedulerInstanceIds = useAtomValue(schedulerInstanceIdsAtom);

  const { mutate, isLoading } = useSubmit();
  const instanceId = instance?.map((item) => item?.instance_id);

  const params = useMemo(() => {
    const filterResources = (_type: string) =>
      delResources?.filter((item) => item?.type === _type)?.map((item) => item.id);
    return {
      instances: instanceId,
      volumes: filterResources('volume'),
      eips: filterResources('eip'),
      cascade_mode: 1,
    };
  }, [instanceId, delResources]);

  useEffect(() => {
    setRead(false);
  }, [step]);

  useEffect(() => {
    setStep(0);
    return () => {
      setTime('');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const nextBtnDisabled = useMemo(() => {
    if (step === 0) {
      return !time && type === '1';
    }
    if (step === 1) {
      return !read;
    }
    return false;
  }, [step, read, time, type]);

  // 立即删除
  const handleDeleteHmmediately = () => {
    mutate(
      {
        action: 'TerminateInstances',
        ...params,
        zone: window.user.zone,
        owner: window.user.user_id,
      },
      {
        onSuccess: () => {
          IaasModal.remove({ name: 'deletion-instance-modal' });
        },
      },
    );
  };

  const handleSuccess = useCallback(() => {
    setStep((v) => v + 1);
    cb?.();
  }, [cb, setStep]);
  // 创建定时删除
  const hanfleCreateScheduler = (ids: string[]) => {
    const targetTime = dayjs(time);
    const schedulers = ids.map((id: string) => ({
      scheduler_name: `auto delete instance[${id}]`,
      repeat: 0,
      notify_event: notifyUser ? 1 : 0,
      notification_lists: notifyUser || undefined,
      hhmm: targetTime.utc().format('HH:mm'),
      ymd: targetTime.utc().format('YYYY-MM-DD'),
      controller: 'instance',
      scheduler_tasks: [
        {
          task_type: 'terminate_instances',
          task_params: {
            cascade_mode: 2,
            resources: [id],
          },
        },
      ],
    }));
    mutate(
      {
        action: 'CreateBatchSchedulers',
        schedulers,
        zone: window.user.zone,
        owner: window.user.user_id,
      },
      { onSuccess: handleSuccess },
    );
  };

  // 修改定时删除
  const handleModifyScheduler = async (ids: string[]) => {
    const targetTime = dayjs(time);
    const schedulers = ids.map((id: string) => {
      return {
        scheduler: id,
        hhmm: targetTime.utc().format('HH:mm'),
        ymd: targetTime.utc().format('YYYY-MM-DD'),
      };
    });
    await mutate(
      {
        action: 'ModifyBatchSchedulerAttributes',
        zone: window.user.zone,
        schedulers,
      },
      { onSuccess: handleSuccess },
    );
  };

  const handleScheduleDelete = () => {
    const err = validateDeletionTime({ time, validTime: 30 });
    const targetTime = dayjs(time);
    const isSame = schedulerInfo?.scheduler && targetTime.isSame(dayjs(`${schedulerInfo.ymd} ${schedulerInfo.hhmm}`));

    if (!isSame && err) {
      Message.error({
        message: err,
      });
      return;
    }

    // 单个主机修改定时时间，时间不变，直接跳过
    if (schedulerInfo?.scheduler && isSame) {
      setStep((v) => v + 1);
      return;
    }
    // 已经创建过定时删除的主机及定时器id;
    const [instanceIds, schedulerIds] = schedulerInstanceIds;
    // 需要创建新的定时器的主机
    const needCreateSchedulerIds = instanceId?.filter((id) => !instanceIds?.includes(id));
    if (needCreateSchedulerIds?.length) {
      hanfleCreateScheduler(needCreateSchedulerIds);
    }
    // 需要修改定时器的主机
    if (schedulerIds?.length) {
      handleModifyScheduler(schedulerIds);
    }
  };

  return (
    <portal.div css={{ position: 'absolute', left: -20, bottom: -8, width: '100%' }}>
      {step === 1 && (
        <portal.div
          css={{
            display: 'flex',
            alignItems: 'center',
            backgroundColor: 'fill.deep',
            height: 40,
            padding: '0 20px',
            width: '100%',
            position: 'relative',
            zIndex: 2,
          }}
        >
          <Checkbox
            value=""
            checked={read}
            onChange={setRead}
            label={t(
              '我已知晓即将删除的资源与关联资源，并了解相关数据风险。',
              'I am aware of the resources and associated resources to be deleted, and understand the related data risks.',
            )}
          />
        </portal.div>
      )}
      <portal.div
        css={{
          display: 'flex',
          justifyContent: 'flex-end',
          padding: '12px 20px 0',
          borderTop: 'border.shallow',
          gap: '8px',
          width: '100%',
        }}
      >
        {step < 2 && (
          <Button onClick={() => IaasModal.remove({ name: 'deletion-instance-modal' })}>{t('取消', 'Cancel')}</Button>
        )}
        {/* 上一步按钮 */}
        {step === 1 && <Button onClick={() => setStep((v) => v - 1)}>{t('上一步', 'Previous Step')}</Button>}
        {/* 下一步 / 确定按钮 */}
        <Button
          type="primary"
          disabled={nextBtnDisabled}
          loading={isLoading}
          onClick={() => {
            if (step === 1) {
              const action = type === '0' ? handleDeleteHmmediately : handleScheduleDelete;
              action();
            } else if (step < 2) {
              setStep((v) => v + 1);
            } else {
              IaasModal.remove({ name: 'deletion-instance-modal' });
              unmountSelf?.();
            }
          }}
        >
          {step === 2 || (step === 1 && type === '0') ? t('确定', 'Confirm') : t('下一步', 'Next Step')}
        </Button>
      </portal.div>
    </portal.div>
  );
}

export default ModalFooter;
