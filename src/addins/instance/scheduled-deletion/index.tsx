import React, { useEffect } from 'react';
import { useSetAtom } from 'jotai';
import NiceModal from '@ebay/nice-modal-react';

import { useMicAppContext } from 'common/hooks';
import { type Instance } from 'common';
import { instanceAtom } from './state';
import ScheduledDeletionModal from './modal-wrap';

interface QiankunProps {
  instance: Instance;
}

function ReplacementCertificateModal(): JSX.Element {
  const { instance } = useMicAppContext<QiankunProps>();
  const setInstance = useSetAtom(instanceAtom);

  useEffect(() => {
    if (instance) {
      setInstance(Array.isArray(instance) ? instance : [instance]);
      NiceModal.show('deletion-instance-modal');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instance]);

  return <ScheduledDeletionModal />;
}

export default ReplacementCertificateModal;
