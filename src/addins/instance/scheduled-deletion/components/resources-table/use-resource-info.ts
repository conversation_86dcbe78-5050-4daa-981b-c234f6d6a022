import { useMemo } from 'react';

import { useRequest } from 'common/hooks';
import { type Volume, type LeaseInfo } from 'common';

interface Props {
  volumeIds: string[];
  eipIds: string[];
}

interface MegeData extends Volume {
  leaseInfo: LeaseInfo;
}
function useVolumesInfo({ volumeIds = [], eipIds = [] }: Props) {
  const supportBilling = window.GLOBAL_CONFIG?.SYSTEM_SETTINGS?.support_billing;

  const { data: volumesInfo } = useRequest(
    {
      action: 'DescribeVolumes',
      zone: window.user.zone,
      volumes: volumeIds,
      offset: 0,
      limit: 999,
    },
    {
      enabled: volumeIds?.length > 0,
      select: (res) => res?.volume_set || [],
    },
  );

  const { data: leaseInfos } = useRequest(
    {
      action: 'GetLeaseInfos',
      zone: window.user.zone,
      owner: window.user.user_id,
      resources: [...volumeIds, ...eipIds],
    },
    {
      enabled: (volumeIds?.length > 0 || eipIds?.length > 0) && !!supportBilling,
      select: (res) => res?.lease_info_set || [],
    },
  );

  const mergedVolumeData = useMemo(() => {
    if (!volumesInfo) return [];

    return volumesInfo
      .map((volume: Volume) => ({
        ...volume,
        leaseInfo: leaseInfos?.find((lease: LeaseInfo) => lease.resource_id === volume.volume_id),
      }))
      ?.filter((item: MegeData) => {
        // 绑定了多个主机的硬盘过滤掉
        const bindOneVm = item?.instances?.length === 1;
        // 包年包月的主机过滤掉
        const notMonthly = item?.leaseInfo?.contract?.charge_mode !== 'monthly';
        return bindOneVm && notMonthly;
      });
  }, [volumesInfo, leaseInfos]);
  // 过滤包年包月的eip
  const filterEips = useMemo(() => {
    return eipIds?.filter((id: string) => {
      const leaseInfo = leaseInfos?.find((lease: LeaseInfo) => lease.resource_id === id);
      return leaseInfo ? leaseInfo.contract?.charge_mode !== 'monthly' : true;
    });
  }, [eipIds, leaseInfos]);

  return [mergedVolumeData, filterEips];
}

export default useVolumesInfo;
