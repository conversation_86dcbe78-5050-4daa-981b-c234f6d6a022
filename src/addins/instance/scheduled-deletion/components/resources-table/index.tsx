import React, { useCallback, useEffect, useMemo } from 'react';
import { portal, Table } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useAtomValue, useSetAtom } from 'jotai';

import type { Instance, Volume } from 'common';
import { useRequest } from 'common/hooks';
import columns from './columns';
import useResourceInfo from './use-resource-info';
import {
  instanceAtom,
  deleteTimeAtom,
  deleteVolumeAtom,
  deleteEipAtom,
  deleteTypeAtom,
  delResourcesAtom,
} from '../../state';

interface TableDate {
  id: string;
  name: string;
  father: string;
  type: string;
  typeName: string;
}

function ResourcesTable(): JSX.Element {
  const instance = useAtomValue(instanceAtom);
  const time = useAtomValue(deleteTimeAtom);
  const delEip = useAtomValue(deleteEipAtom);
  const delVolume = useAtomValue(deleteVolumeAtom);
  const delType = useAtomValue(deleteTypeAtom);
  const setDelResource = useSetAtom(delResourcesAtom);

  const instanceIds = instance?.map((item) => item.instance_id);

  // 将要删除的主机下所有绑定的volumeIDs
  const [allVolIds, allEipIds] = useMemo(() => {
    const volIdsSet = new Set<string>();
    const eipIdsSet = new Set<string>();

    instance?.forEach(({ volume_ids: volumeIds = [], eips = [] }) => {
      volumeIds.forEach(volIdsSet.add, volIdsSet);
      eips.forEach(({ eip_id: eipId }) => eipIdsSet.add(eipId));
    });
    return [Array.from(volIdsSet), Array.from(eipIdsSet)];
  }, [instance]);

  // 所有需要删除的resourceIds，去除包年包月的资源
  const [volumesInfo, filterEipIds] = useResourceInfo({ volumeIds: allVolIds, eipIds: allEipIds });

  // 定时删除策略的数据
  const { data: policesIds, isFetching } = useRequest(
    {
      action: 'DescribeCascadePolicies',
      zone: window.user.zone,
      resources: instanceIds,
      resource_type: 'instance',
      on_operation: 'delete',
      do_operation: 'delete',
      status: 'active',
    },
    {
      enabled: instanceIds?.length > 0,
      select: (_data) => {
        const cascadePolices = _data?.cascade_polices || [];
        if (cascadePolices?.length) {
          return cascadePolices.map((item: { related_resource_id: string }) => item.related_resource_id);
        }
        return [];
      },
    },
  );

  const policySet = useMemo(() => new Set(Array.isArray(policesIds) ? policesIds : []), [policesIds]);
  const filterInPolicy = useCallback((id) => policySet.has(id), [policySet]);

  const dataResource = useMemo(() => {
    return instance?.reduce((acc: TableDate[], item: Instance) => {
      const curInstance = {
        id: item?.instance_id,
        name: item?.instance_name || '-',
        father: '-',
        type: 'instance',
        typeName: t('云服务器', 'Instance'),
      };

      const delVolumes = delVolume ? item?.volume_ids : item?.volume_ids?.filter(filterInPolicy);
      const curVolumes = delVolumes
        ?.map((volId: string) => {
          const volInfo = volumesInfo?.find((vol: Volume) => vol?.volume_id === volId);
          return volInfo
            ? {
                id: volInfo?.volume_id,
                name: volInfo?.volume_name || '-',
                father: item?.instance_id,
                type: 'volume',
                typeName: t('云硬盘', 'Volume'),
              }
            : undefined;
        })
        .filter(Boolean) as TableDate[];
      const eipids = item?.eips?.map((eip) => eip?.eip_id);
      const delEips = delEip ? eipids : eipids?.filter(filterInPolicy);
      const curEips = delEips
        ?.map((eipId: string) => {
          const canDel = filterEipIds.includes(eipId);
          return canDel
            ? {
                id: eipId,
                name: item?.eips?.find((eip) => eip?.eip_id === eipId)?.eip_name || '-',
                father: item?.instance_id,
                type: 'eip',
                typeName: t('弹性IP', 'EIP'),
              }
            : undefined;
        })
        .filter(Boolean) as TableDate[];

      return [...acc, curInstance, ...curVolumes, ...curEips];
    }, [] as TableDate[]);
  }, [delEip, delVolume, filterInPolicy, instance, volumesInfo, filterEipIds]);

  useEffect(() => {
    setDelResource(dataResource);
  }, [dataResource, setDelResource]);

  return (
    <portal.div css={{ margin: '24px 0' }}>
      <portal.div css={{ lineHeight: '20px', fontWeight: '500', marginBottom: 8 }}>
        {delType === '1'
          ? t(`您将在 ${time} 定时删除以下资源:`, `You will delete the following resources at ${time} timer:`)
          : t('您将立即删除以下资源:', 'you will immediately delete the following resources:')}
      </portal.div>
      <Table rowKey="id" dataSource={dataResource} columns={columns} loading={isFetching} />
    </portal.div>
  );
}

export default ResourcesTable;
