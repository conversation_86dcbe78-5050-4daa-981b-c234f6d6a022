import React from 'react';
import { portal, Alert, Checkbox } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useAtom, useAtomValue } from 'jotai';

import { deleteEipAtom, deleteVolumeAtom, instanceAtom } from '../state';

function AssociatedResources(): JSX.Element {
  const [deleteEip, setDeleteEip] = useAtom(deleteEipAtom);
  const [deleteVol, setDeleteVol] = useAtom(deleteVolumeAtom);
  const instance = useAtomValue(instanceAtom);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const supportSnapshot = !!(window?.GLOBAL_CONFIG as any)?.support_volume_snapshot;
  const canSnapshot = instance?.some((item) => item.is_able_local_snapshot);

  return (
    <portal.div css={{ margin: '20px 0 0 12px', whiteSpace: 'pre-line' }}>
      <portal.div css={{ fontWeight: '500', marginBottom: 8 }}>
        {t('设置删除关联资源', 'Setting up deleted associated resources')}
      </portal.div>
      <Alert
        type="warning"
        closable={false}
        showIcon
        description={
          supportSnapshot && canSnapshot
            ? t(
                '设置跟随云主机删除后，数据盘将会随关联云主机一起进入回收站，公网IP将会自动释放。数据盘一旦删除，数据盘所存资源将会一起删除，如果云服务器已进行快照操作，所有快照也将一起删除，请在删除前确定数据是否需要备份。如数据盘、公网 IP 继续保留，将持续收取费用。',
                'After the setting follows the cloud host deletion, the data disk will go into the recycle bin with the associated cloud host, and the public IP will be released automatically. Once the data disk is deleted, the resources stored in the data disk will be deleted together, if the cloud server has performed snapshot operations, all snapshots will also be deleted together. Please make sure whether the data needs to be backed up before deletion. If the data disk and the public IP are retained, there will',
              )
            : t(
                '设置跟随云主机删除后，数据盘将会随关联云主机一起进入回收站，公网IP将会自动释放。数据盘一旦删除，数据盘所存资源将会一起删除，请在删除前确定数据是否需要备份。如数据盘、公网 IP 继续保留，将持续收取费用。',
                'After the setting follows the cloud host deletion, the data disk will go into the recycle bin with the associated cloud host, and the public IP will be released automatically. Once the data disk is deleted, the resources stored in the data disk will be deleted together. Please make sure whether the data needs to be backed up before deletion. If the data disk and the public IP are retained, there will be a continuous charge.',
              )
        }
      />
      <Checkbox
        value="volume"
        checked={deleteVol}
        onChange={setDeleteVol}
        label={t('删除云服务器挂载的数据盘', 'Deleting instance mounted data disks')}
        description={t('包年包月数据盘不可跟随删除', 'Yearly and monthly data disks cannot be deleted on the fly')}
        css={{ margin: '12px 0 8px', display: 'block' }}
      />
      <Checkbox
        value="eip"
        checked={deleteEip}
        onChange={setDeleteEip}
        label={t('释放云服务器挂载的公网 IP', "Release the instance's mounted public IP")}
      />
    </portal.div>
  );
}

export default AssociatedResources;
