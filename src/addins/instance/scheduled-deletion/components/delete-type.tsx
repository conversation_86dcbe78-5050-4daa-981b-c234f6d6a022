import React, { useEffect } from 'react';
import { portal, RadioGroup } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useAtom, useSetAtom } from 'jotai';

import { deleteTypeAtom, deleteEipAtom, deleteVolumeAtom } from '../state';

const OPTIONS = [
  {
    label: t('立即删除', 'Delete immediately'),
    value: '0',
  },
  {
    label: t('定时删除', 'Scheduled deletion'),
    value: '1',
  },
];

function DeleteType(): JSX.Element {
  const [type, setType] = useAtom(deleteTypeAtom);
  const setDeleteEip = useSetAtom(deleteEipAtom);
  const setDeleteVol = useSetAtom(deleteVolumeAtom);

  useEffect(() => {
    if (type === '1') {
      setDeleteEip(false);
      setDeleteVol(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type]);

  return (
    <portal.div css={{ display: 'flex', alignItems: 'center', marginBottom: 24, lineHeight: '20px', marginLeft: 12 }}>
      <portal.div css={{ fontWeight: '500', width: 72 }}>{t('删除方式', 'Delete type')}</portal.div>
      <RadioGroup options={OPTIONS} value={type} onChange={setType} />
    </portal.div>
  );
}

export default DeleteType;
