import React from 'react';
import { portal, Table } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useAtomValue } from 'jotai';

import { instanceAtom } from '../state';

const columns = [
  {
    dataIndex: 'instance_id',
    key: 'instance_id',
    title: 'ID',
  },
  {
    dataIndex: 'instance_name',
    key: 'instance_name',
    title: t('名称', 'Instance Name'),
    cellRender: (value: unknown) => value || '-',
  },
];

function InstanceTable(): JSX.Element {
  const instance = useAtomValue(instanceAtom);

  return (
    <portal.div css={{ margin: '24px 0' }}>
      <portal.div css={{ lineHeight: '20px', fontWeight: '500', marginBottom: 8 }}>
        {t('您正在删除以下云服务器：', 'You are deleting the following instance:')}
      </portal.div>
      <Table rowKey="instance_id" dataSource={instance} columns={columns} />
    </portal.div>
  );
}

export default InstanceTable;
