import React from 'react';
import { portal, Icon } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useAtomValue } from 'jotai';

import { delResourcesAtom, deleteTimeAtom } from '../state';

function ConfirmInfo(): JSX.Element {
  const time = useAtomValue(deleteTimeAtom);
  const delResources = useAtomValue(delResourcesAtom);

  const ids = (delResources.map((item) => item.id) || [])?.join('、');

  return (
    <portal.div css={{ padding: '10px 14px', marginTop: 24, display: 'flex', gap: 12, justifyContent: 'flex-start' }}>
      <Icon name="success_fill" size={20} fill="#15A675" style={{ marginTop: 3 }} />
      <portal.div>
        <portal.div css={{ lineHeight: '24px', fontSize: '16px', fontWeight: '500' }}>
          {t('定时删除设置成功', 'Timed deletion settings successfully set')}
        </portal.div>
        <portal.div css={{ lineHeight: '20px', marginTop: 8 }}>
          {ids} {t('将在', 'will be timed out on')}
          <portal.span css={{ color: 'brand.default' }}>&nbsp;{time}&nbsp;</portal.span>
          {t('进行定时删除。', '')}
        </portal.div>
      </portal.div>
    </portal.div>
  );
}

export default ConfirmInfo;
