import React, { useEffect } from 'react';
import { portal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';

import { ScheduledDeletionPicker, ScheduledDeletionProps } from 'common';
import { deleteTimeAtom, schedulerInfoAtom, notifyUser<PERSON>tom } from '../state';

function DeleteTime(): JSX.Element {
  const [time, setTime] = useAtom(deleteTimeAtom);
  const schedulerInfo = useAtomValue(schedulerInfoAtom);
  const setNotifyUser = useSetAtom(notifyUserAtom);

  useEffect(() => {
    if (schedulerInfo?.ymd && schedulerInfo?.hhmm) {
      setTime(`${schedulerInfo.ymd} ${schedulerInfo.hhmm}`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [schedulerInfo]);

  const handleChangeTime = (val: ScheduledDeletionProps) => {
    setTime(val?.localTime);
  };

  return (
    <portal.div
      css={{ display: 'flex', alignItems: 'flex-start', marginBottom: 24, lineHeight: '20px', marginLeft: 12 }}
    >
      <portal.div css={{ fontWeight: '500', marginTop: 6, width: 72 }}>{t('删除时间', 'Delete time')}</portal.div>
      <div>
        <ScheduledDeletionPicker
          value={time}
          needDefaultValue
          onChange={handleChangeTime}
          setNodifyUser={setNotifyUser}
          helpText={[
            t(
              '设置定时删除时间后，系统将在设定时间自动删除云服务器，请提前做好数据备份。',
              'After setting the timed deletion time, the system will automatically delete the instance at the set time, please make a good backup of your data in advance.',
            ),
            t(
              '设置的删除时间需要晚于当前时间 1小时，且不能超过1年。',
              'The set deletion time needs to be 1 hour later than the current time and cannot be more than 1 year.',
            ),
          ]}
        />
      </div>
    </portal.div>
  );
}

export default DeleteTime;
