import React from 'react';
import { Steps, Step, portal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useAtomValue } from 'jotai';

import { stepAtom } from '../state';

function StepField(): JSX.Element {
  const step = useAtomValue(stepAtom);

  return (
    <portal.div css={{ padding: '0 56px' }}>
      <Steps current={step}>
        <Step title={t('删除设置', 'Delete Settings')} />
        <Step title={t('删除确认', 'Delete Confirmation')} />
        <Step title={t('完成', 'Complete')} />
      </Steps>
    </portal.div>
  );
}

export default StepField;
