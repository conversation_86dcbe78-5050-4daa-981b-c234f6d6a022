import React from 'react';
import { portal } from '@pitrix/portal-ui';
import { useAtomValue } from 'jotai';

import { stepAtom, deleteTypeAtom } from './state';
import ModalFooter from './modal-footer';
import Step from './components/step';
import InstanceTable from './components/instance-table';
import DeleteType from './components/delete-type';
import DeleteTime from './components/delete-time';
import AssociatedResources from './components/associated-resources';

import ResourcesTable from './components/resources-table';
import ConfirmInfo from './components/confirm-info';

function ModalContent() {
  const step = useAtomValue(stepAtom);
  const type = useAtomValue(deleteTypeAtom);

  return (
    <portal.div css={{ position: 'relative', paddingBottom: 54 }}>
      <Step />
      <portal.div css={{ maxHeight: 'calc(100vh - 220px)', overflow: 'auto' }}>
        {step === 0 && (
          <>
            <InstanceTable />
            <DeleteType />
            {type === '1' && <DeleteTime />}
            {type === '0' && <AssociatedResources />}
          </>
        )}
        {step === 1 && <ResourcesTable />}
        {step === 2 && <ConfirmInfo />}
      </portal.div>
      <ModalFooter />
    </portal.div>
  );
}

export default ModalContent;
