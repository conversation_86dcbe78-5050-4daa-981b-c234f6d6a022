import React from 'react';
import { Alert } from '@pitrix/portal-ui';
import NiceModal from '@ebay/nice-modal-react';
import { t, requestDescribeInstances, DescribeInstanceRequest, Image, Instance } from '@pitrix/portal-widget';

import { ModalTable, BaseModal, request, Status } from 'common';
import { useMicAppContext } from 'common/hooks';

const FILTERS = [
  {
    text: '全部',
    value: ['pending', 'running', 'stopped', 'suspended', 'rescuing'],
  },
  {
    text: '等待中',
    value: 'pending',
  },
  {
    text: '运行中',
    value: 'running',
  },
  {
    text: '已关机',
    value: 'stopped',
  },
  {
    text: '已暂停',
    value: 'suspended',
  },
  {
    text: '救援中',
    value: 'rescuing',
  },
];

const columns = [
  {
    title: '名称',
    dataIndex: 'instance_name',
    key: 'instance_name',
  },
  {
    title: 'ID',
    key: 'instance_id',
    dataIndex: 'instance_id',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    filters: FILTERS,
    hasAll: false,
    width: 120,
    render: (_key: string, row: Instance) => <Status instance={row} />,
  },
];

interface SelectInfo {
  add: React.Key[];
  cancel: React.Key[];
  rows: Instance;
}

interface MicAppProps {
  image: Image;
}

export default function ImageManage(): JSX.Element {
  const { image, unmountSelf, cb } = useMicAppContext<MicAppProps>();

  const [selectInfo, setInfo] = React.useState<SelectInfo>({
    add: [],
    cancel: [],
    rows: {} as unknown as Instance,
  });

  const fetchData = <P,>(params?: P) =>
    requestDescribeInstances({
      zone: window?.user?.zone,
      owner: window?.user?.user_id,
      offset: 0,
      limit: 10,
      sort_key: 'create_time',
      status: ['pending', 'running', 'stopped', 'suspended', 'rescuing'],
      reverse: 1,
      verbose: 0,
      mount_image_id: image?.image_id,
      support_cdrom: 1,
      ...params,
    } as DescribeInstanceRequest);

  const handleSubmit = async () => {
    if (!selectInfo.add?.length) return;
    const res = await request({
      params: {
        action: 'AttachImage',
        instance: selectInfo.add?.[0],
        image: image?.image_id,
        zone: window?.user?.zone,
        owner: window?.user?.user_id,
      },
    });
    if (res && res.ret_code === 0) {
      unmountSelf?.();
      cb?.(res);
    }
  };

  React.useEffect(() => {
    if (image) {
      NiceModal.show('image-manage-modal');
    }
  }, [image]);

  return (
    <NiceModal.Provider>
      <BaseModal
        id="image-manage-modal"
        name="image-manage-modal"
        width={700}
        title={t('镜像管理', 'Image management')}
        onCancel={() => {
          unmountSelf?.();
        }}
        onAsyncOk={async (_data, close) => {
          await handleSubmit();
          close?.();
          unmountSelf?.();
        }}
        disabled={!(selectInfo.add?.length || selectInfo.cancel?.length)}
      >
        <Alert
          type="info"
          message={t(
            '由于 cdrom 不支持热插拔，所以对于未安装 cdrom 功能的VM要进行重启操作，已安装的可直接挂载。',
            'Due to the fact that cdrom does not support hot swapping, VMs without cdrom functionality need to be restarted. Installed VMs can be directly mounted.',
          )}
          closable={false}
          css={{ marginBottom: 20, width: '100%' }}
        />
        <ModalTable<Instance>
          placeholder={t('输入资源名称/ID进行搜索', 'Enter resource name/ID for search')}
          selectType="radio"
          showChecked
          fetchData={fetchData}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          columns={columns as any}
          rowKey="instance_id"
          resultKey="instance_set"
          renderSelectItem={(item: Instance) => {
            const { instance_id, instance_name } = item;
            return <span>{`${instance_name || '-'}/${instance_id}`}</span>;
          }}
          onSelect={(newData: React.Key[], cancelData: React.Key[], rows: Instance[]) => {
            setInfo({
              add: newData,
              cancel: cancelData,
              rows: rows?.length ? rows?.[0] : selectInfo.rows,
            });
          }}
        />
      </BaseModal>
    </NiceModal.Provider>
  );
}
