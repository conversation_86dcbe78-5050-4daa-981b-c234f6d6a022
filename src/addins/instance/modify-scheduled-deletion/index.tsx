import React, { useEffect } from 'react';
import NiceModal from '@ebay/nice-modal-react';

import { useMicAppContext } from 'common/hooks';

import ModifyModal from './modify-modal';

interface QiankunProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  instance: any;
}

function ModifyScheduledDeletion(): JSX.Element {
  const { instance } = useMicAppContext<QiankunProps>();

  useEffect(() => {
    if (instance) {
      NiceModal.show('scheduled-deletion-modal');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instance]);

  return <ModifyModal />;
}

export default ModifyScheduledDeletion;
