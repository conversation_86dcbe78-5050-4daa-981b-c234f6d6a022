import React, { useEffect, useState, useRef } from 'react';
import { portal, Alert, Message } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import dayjs from 'dayjs';
import { get } from 'lodash-es';
import utc from 'dayjs/plugin/utc';

import { Instance, Field, request, BaseModal, ScheduledDeletionPicker, ScheduledDeletionProps } from 'common';
import { useMicAppContext, useRequest } from 'common/hooks';
import { validateDeletionTime } from 'common/utils';

dayjs.extend(utc);

export default function ScheduledDeletion(): JSX.Element {
  const { unmountSelf, instance, onOk } = useMicAppContext<{ instance: Instance }>();
  const [time, setTime] = useState('');
  const [taskId, setTaskId] = useState('');
  const [notifyUser, setNodifyUser] = useState<string[] | undefined>();
  const schedulerTime = useRef<dayjs.Dayjs | null>(null);

  const { data } = useRequest(
    {
      action: 'DescribeResourceSchedulerTasks',
      resources: [instance?.instance_id],
      resource_type: 'instance',
      task_type: 'terminate_instances',
      controller: 'instance',
      verbose: 1,
      zone: window.user.zone,
    },
    {
      enabled: !!instance?.instance_id,
    },
  );

  useEffect(() => {
    const dataSet = data?.resource_scheduler_tasks;
    const instanceScheduler = get(dataSet?.[instance?.instance_id], [0, 'scheduler'], {});
    const ymd = get(instanceScheduler, 'ymd', '');
    const hhmm = get(instanceScheduler, 'hhmm', '');

    setTaskId(instanceScheduler?.scheduler_id);
    if (ymd && hhmm) {
      const curTime = dayjs.utc(`${ymd} ${hhmm}`).local();
      setTime(curTime.format('YYYY-MM-DD HH:mm'));
      schedulerTime.current = curTime;
    }
  }, [data, instance?.instance_id]);

  const handleOk = async () => {
    const isErr = validateDeletionTime({ time, validTime: 30 });
    const targetTime = dayjs(time);
    const isSame = schedulerTime.current && targetTime.isSame(schedulerTime.current);
    if (!isSame && isErr) {
      Message.error({
        message: t(
          '设置的删除时间需晚于当前时间1小时，且不能超过1年',
          'The deletion time set must be 1 hour later than the current time and cannot exceed 1 year',
        ),
      });
      return;
    }

    if (taskId) {
      if (isSame) {
        unmountSelf?.();
        return;
      }
      await request({
        params: {
          action: 'ModifySchedulerAttributes',
          scheduler: taskId,
          ymd: targetTime.utc().format('YYYY-MM-DD'),
          hhmm: targetTime.utc().format('HH:mm'),
          owner: window.user.user_id,
          zone: window.user.zone,
        },
      }).then((res) => {
        if (res?.ret_code === 0) {
          onOk?.();
          unmountSelf?.();
        }
      });
      return;
    }
    await request({
      params: {
        action: 'CreateScheduler',
        zone: window.user.zone,
        scheduler_name: `auto delete instance[${instance?.instance_id}]`,
        ymd: targetTime.utc().format('YYYY-MM-DD'),
        hhmm: targetTime.utc().format('HH:mm'),
        notify_event: notifyUser ? 1 : 0,
        notification_lists: notifyUser,
        controller: 'instance',
        repeat: 0,
        scheduler_tasks: [
          {
            task_type: 'terminate_instances',
            task_params: {
              cascade_mode: 2,
              resources: [instance?.instance_id],
            },
          },
        ],
      },
    }).then((res) => {
      if (res?.ret_code === 0) {
        onOk?.();
        unmountSelf?.();
      }
    });
  };

  const handleChangeTime = (val: ScheduledDeletionProps) => {
    setTime(val?.localTime);
  };

  return (
    <BaseModal
      id="scheduled-deletion-modal"
      name="scheduled-deletion-modal"
      width={600}
      title={
        taskId
          ? t('修改定时删除时间', 'Modify the scheduled deletion time')
          : t('设置定时删除时间', 'Set the scheduled deletion time')
      }
      onCancel={() => {
        unmountSelf?.();
      }}
      disabled={!time}
      onAsyncOk={async () => {
        await handleOk();
      }}
    >
      <portal.div>
        {!time && (
          <Alert
            showIcon
            closable={false}
            type="warning"
            css={{ marginBottom: 16 }}
            description={t(
              '设置定时删除时间后，系统将在设定时间自动删除云服务器，请谨慎操作。',
              'After setting the scheduled deletion time the system will automatically delete the cloud server at the set time please operate with caution',
            )}
          />
        )}

        <Field label={t('删除时间', 'Delete time')} align="left">
          <ScheduledDeletionPicker
            value={time}
            needDefaultValue
            onChange={handleChangeTime}
            setNodifyUser={(ids) => setNodifyUser(ids)}
            helpText={t(
              '设置的删除时间需要晚于当前时间 1小时，且不能超过1年。',
              'The deletion time set must be 1 hour later than the current time and cannot exceed 1 year',
            )}
          />
        </Field>
      </portal.div>
    </BaseModal>
  );
}
