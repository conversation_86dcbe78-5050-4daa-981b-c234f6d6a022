import React, { useState } from 'react';
import NiceModal from '@ebay/nice-modal-react';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { Alert, portal, Icon } from '@pitrix/portal-ui';
import { t, Instance } from '@pitrix/portal-widget';

import { BaseModal, ConfirmContent, request } from 'common';
import { useMicAppContext } from 'common/hooks';

interface MicAppProps {
  instance: Instance;
}

const StartMap = [
  {
    label: t('HardDisk（硬盘）', 'HardDisk）'),
    value: '0',
  },
  {
    label: t('CD-ROM（光驱）', 'CD-ROM '),
    value: '1',
  },
];

const contentCss = {
  display: 'flex',
  alignItems: 'center',
  marginLeft: '8px',
  width: 272,
  height: 32,
  border: 'neutral.2',
  borderRadius: '2px',
  padding: '0 12px',
  lineHeight: '32',
  backgroundColor: 'neutral.1',
} as const;

export default function StartupManage() {
  const { unmountSelf, instance, cb } = useMicAppContext<MicAppProps>();

  const [startMap, setStartMap] = useState(StartMap);

  const handleSubmit = async () => {
    const action = instance?.status === 'running' ? 'RestartInstances' : 'StartInstances';
    const res = await request({
      params: {
        action,
        instances: [instance?.instance_id],
        cdrom_start: Number(startMap[0].value),
        zone: window?.user?.zone,
        owner: window?.user?.user_id,
      },
    });
    if (res && res.ret_code === 0) {
      unmountSelf?.();
      cb?.(res);
    }
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }
    const newItems = [...startMap];
    const [removed] = newItems.splice(result.source.index, 1);
    newItems.splice(result.destination.index, 0, removed);

    setStartMap(newItems);
  };

  const handleCloseModal = (id: string) => {
    NiceModal.remove(id);
  };

  React.useEffect(() => {
    if (instance) {
      NiceModal.show('startup-manage');
    }
  }, [instance]);

  return (
    <>
      <BaseModal
        id="startup-manage"
        name="startup-manage"
        width={500}
        title={t('设置启动项顺序', 'Setting the startup item order')}
        onCancel={() => {
          unmountSelf?.();
        }}
        onOk={() => {
          NiceModal.show('startup_confirm_modal');
        }}
        disabled={startMap[0].value === '0'}
      >
        <portal.div css={{ width: '100%' }}>
          <Alert
            showIcon
            type="info"
            message={t(
              '启动项顺序设置将在下一次启动时生效',
              'The startup item order setting will take effect on the next startup',
            )}
            closable={false}
            css={{ marginBottom: 20, width: '100%' }}
          />
          <portal.div css={{ display: 'flex', alignItems: 'center', margin: '16px 0 16px 12px', color: 'text.base' }}>
            <portal.div css={{ textBody: 'sBold', minWidth: 72 }}>{t('云服务器', 'Instance')}</portal.div>
            <portal.span>
              <portal.span>{instance?.instance_name || '-'}</portal.span>
              &nbsp;|&nbsp;
              <portal.span css={{ color: 'text.placeholder' }}>{instance?.instance_id}</portal.span>
            </portal.span>
          </portal.div>

          <portal.div css={{ height: 92, marginLeft: 12 }}>
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="droppable">
                {(provided) => (
                  <div ref={provided.innerRef} {...provided.droppableProps}>
                    {startMap.map((item, index) => (
                      <Draggable draggableId={item.value} key={item.value} index={index}>
                        {(_provided) => (
                          <portal.div
                            ref={_provided.innerRef}
                            {..._provided.draggableProps}
                            {..._provided.dragHandleProps}
                            key={item.value}
                            css={{ display: 'flex', alignItems: 'center', padding: '6px 0' }}
                          >
                            <Icon
                              name={`${index === 0 ? 'one' : 'two'}_circle_duotone`}
                              size={24}
                              style={{ marginRight: 4 }}
                            />
                            <portal.span css={{ color: 'text.placeholder', _hover: { color: 'text.base' } }}>
                              <Icon name="drag_fill" size={18} />
                            </portal.span>
                            <portal.div css={contentCss}>{item.label}</portal.div>
                          </portal.div>
                        )}
                      </Draggable>
                    ))}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </portal.div>
        </portal.div>
      </BaseModal>
      <BaseModal
        id="startup_confirm_modal"
        name="startup_confirm_modal"
        width={600}
        title={t('提示', 'Prompts')}
        onCancel={() => {
          handleCloseModal('startup_confirm_modal');
        }}
        onAsyncOk={async (_data, close) => {
          await handleSubmit();
          close?.();
          handleCloseModal('startup-manage');
          unmountSelf?.();
        }}
        bordered={false}
      >
        <ConfirmContent
          type="error"
          title={t(
            `您的云服务器${instance?.status === 'stopped' ? '已关机' : '正在运行中'}，是否要立即${
              instance?.status === 'stopped' ? '开启' : '重启'
            }云服务器改变启动顺序？`,
            `Your cloud server ${instance?.status === 'stopped' ? ' Shutdown' : 'Running'}, should the immediate ${
              instance?.status === 'stopped' ? ' Started' : 'Restarted'
            } the cloud server to change the startup order?`,
          )}
        />
      </BaseModal>
    </>
  );
}
