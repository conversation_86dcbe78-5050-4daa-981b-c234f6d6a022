import React, { useCallback } from 'react';
import { HotPlugLimitsModal as HotPlugLimits } from 'pages/instance/list/modals';
import { useMicAppContext } from 'common/hooks';

interface HotPlugLimitsExtraParams {
  instanceClass: number;
  instanceId: string;
  onMounted: () => void;
}

const HotPlugLimitsQiankun = () => {
  const { unmountSelf, onOk, onCancel, onMounted, zone, owner, instanceId } =
    useMicAppContext<HotPlugLimitsExtraParams>();

  const onSuccessCb = useCallback(() => {
    onOk?.();
    unmountSelf?.();
  }, [onOk, unmountSelf]);
  const onCancelCb = useCallback(() => {
    onCancel?.();
    unmountSelf?.();
  }, [onCancel, unmountSelf]);
  return (
    <HotPlugLimits
      instanceId={instanceId}
      zone={zone}
      owner={owner}
      onOk={onSuccessCb}
      onCancel={onCancelCb}
      onMounted={onMounted}
    />
  );
};

export default HotPlugLimitsQiankun;
