import React, { useMemo } from 'react';
import NiceModal from '@ebay/nice-modal-react';
import { Table, Column } from '@pitrix/portal-ui';
import { t, Instance, useDescribeInstancesQuery, DescribeInstanceRequest } from '@pitrix/portal-widget';

import { ConfirmContent, BaseModal, request } from 'common';
import { useMicAppContext } from 'common/hooks';

interface MicAppProps {
  instance: Instance;
}

type Image = {
  image_id: string;
  image_name: string;
};

interface InstanceInfo extends Instance {
  mount_image: Image;
}

const ID = 'unmount_iptical_drive';

const columns: Column<Image>[] = [
  {
    title: t('名称', 'Name'),
    dataIndex: 'image_name',
    key: 'image_name',
  },
  {
    title: 'ID',
    key: 'image_id',
    dataIndex: 'image_id',
  },
];

function UnmountOpticalDrive(): JSX.Element {
  const { unmountSelf, instance, cb } = useMicAppContext<MicAppProps>();

  const { data: instancesRes, isLoading } = useDescribeInstancesQuery(
    {
      zone: instance.zone_id,
      owner: window.user.user_id,
      offset: 0,
      limit: 10,
      instances: [instance.instance_id],
      mount_detail: 1,
    } as unknown as DescribeInstanceRequest,
    {
      enabled: !!instance,
    },
  );

  const imageInfo = useMemo(() => {
    const instanceSet = instancesRes?.instance_set as InstanceInfo[];
    if (instanceSet?.length) {
      return instanceSet?.[0]?.mount_image || {};
    }
    return {} as Image;
  }, [instancesRes]);

  const imageData = useMemo(() => {
    return imageInfo?.image_id ? [imageInfo] : [];
  }, [imageInfo]);

  const onSubmit = async () => {
    const res = await request({
      params: {
        action: 'DetachImage',
        instance: instance?.instance_id,
        zone: window?.user?.zone,
        owner: window?.user?.user_id,
        image: imageInfo?.image_id,
      },
    });
    if (res && res.ret_code === 0) {
      unmountSelf?.();
      cb?.(res);
    }
  };

  React.useEffect(() => {
    if (instance) {
      NiceModal.show(ID);
    }
  }, [instance]);

  return (
    <BaseModal
      width={600}
      id={ID}
      name={ID}
      title={t('卸载光驱', 'Uninstall optical drive')}
      okType="danger"
      okText={t('卸载', 'uninstall')}
      onAsyncOk={onSubmit}
      onCancel={() => {
        unmountSelf?.();
      }}
      disabled={!imageInfo?.image_id}
      bordered={false}
    >
      <ConfirmContent
        type="error"
        title={t(
          `确定要卸载 "${imageInfo?.image_name ?? ''}" 吗？`,
          `Sure you want to uninstall ${imageInfo?.image_name ?? ''}?`,
        )}
      >
        <Table loading={isLoading} rowKey="image_id" dataSource={imageData} columns={columns} />
      </ConfirmContent>
    </BaseModal>
  );
}

export default UnmountOpticalDrive;
