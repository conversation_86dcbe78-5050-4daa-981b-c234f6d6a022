import React from 'react';
import { Alert } from '@pitrix/portal-ui';
import NiceModal from '@ebay/nice-modal-react';
import { t, requestDescribeImages, DescribeImagesRequest, Image, Instance } from '@pitrix/portal-widget';

import { ModalTable, BaseModal, IaasModal, ConfirmContent, request } from 'common';
import { useMicAppContext } from 'common/hooks';

const columns = [
  {
    title: '名称',
    dataIndex: 'image_name',
    key: 'image_name',
    render: (_key: string, row: Image) => <span>{`${row?.image_name}/${row?.image_id}`}</span>,
  },
  {
    title: '平台',
    key: 'platform',
    dataIndex: 'platform',
  },
  {
    title: '容量(G)',
    dataIndex: 'size',
    key: 'size',
  },
];

interface SelectInfo {
  add: React.Key[];
  cancel: React.Key[];
  rows: Image;
}

interface MicAppProps {
  instance: Instance & { mount_status: boolean };
}

export default function IsoManage(): JSX.Element {
  const { instance, unmountSelf, cb } = useMicAppContext<MicAppProps>();

  const [selectInfo, setInfo] = React.useState<SelectInfo>({
    add: [],
    cancel: [],
    rows: {} as Image,
  });

  const detachImage = async () => {
    const res = await request({
      params: {
        action: 'DetachImage',
        instance: instance?.instance_id,
        zone: window?.user?.zone,
        owner: window?.user?.user_id,
      },
    });
    if (res && res.ret_code === 0) {
      unmountSelf?.();
      cb?.(res);
    }
  };

  const fetchData = <P,>(params?: P) =>
    requestDescribeImages({
      zone: window?.user?.zone,
      owner: window?.user?.user_id,
      offset: 0,
      limit: 10,
      sort_key: 'create_time',
      status: ['pending', 'available', 'suspended'],
      reverse: 1,
      verbose: 0,
      provider: 'self',
      image_type: 1,
      ...params,
    } as unknown as DescribeImagesRequest);

  const handleDetachImage = async () => {
    IaasModal.create({
      name: 'instance-detach-image-modal',
      bordered: false,
      children: (
        <ConfirmContent
          type="info"
          title={t('提示', '')}
          tip={t(
            `确定要卸载当前云服务器的镜像吗？`,
            'Are you sure you want to uninstall the image of the current instance?',
          )}
        />
      ),
      okText: t('确定', 'Enter'),
      okType: 'danger',
      onAsyncOk: async (close) => {
        await detachImage();
        close?.();
        NiceModal.hide('instance-iso-manage');
      },
    });
  };

  const handleSubmit = async () => {
    const addId = selectInfo.add?.[0];
    if (!addId) {
      return;
    }
    const res = await request({
      params: {
        action: 'AttachImage',
        instance: instance?.instance_id,
        image: addId,
        zone: window?.user?.zone,
        owner: window?.user?.user_id,
      },
    });
    if (res && res.ret_code === 0) {
      cb?.(res);
    }
  };

  React.useEffect(() => {
    if (instance) {
      NiceModal.show('iso-manage-modal');
    }
  }, [instance]);

  return (
    <NiceModal.Provider>
      <BaseModal
        id="iso-manage-modal"
        name="iso-manage-modal"
        width={700}
        title={t('镜像管理', 'Image management')}
        onCancel={() => {
          unmountSelf?.();
        }}
        onAsyncOk={async (_data, close) => {
          await handleSubmit();
          close?.();
          unmountSelf?.();
        }}
        disabled={!selectInfo.add?.length}
      >
        <Alert
          type="info"
          message={t(
            '由于 cdrom 不支持热插拔，所以对于未安装 cdrom 功能的VM要进行重启操作，已安装的可直接挂载。',
            'Due to the fact that cdrom does not support hot swapping, VMs without cdrom functionality need to be restarted. Installed VMs can be directly mounted.',
          )}
          closable={false}
          css={{ marginBottom: 20, width: '100%' }}
        />
        <ModalTable<Image>
          placeholder={t('输入资源名称/ID进行搜索', 'Enter resource name/ID for search')}
          selectType="radio"
          showChecked
          fetchData={fetchData}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          columns={columns as any}
          rowKey="image_id"
          resultKey="image_set"
          renderSelectItem={(item: Image) => {
            const { image_id, image_name } = item;
            return <span>{`${image_name || '-'}/${image_id}`}</span>;
          }}
          operateText={instance?.mount_status ? t('取消挂载', 'Unmount') : ''}
          operateClick={handleDetachImage}
          onSelect={(newData: React.Key[], cancelData: React.Key[], rows: Image[]) => {
            setInfo({
              add: newData,
              cancel: cancelData,
              rows: rows?.length ? rows?.[0] : selectInfo.rows,
            });
          }}
        />
      </BaseModal>
    </NiceModal.Provider>
  );
}
