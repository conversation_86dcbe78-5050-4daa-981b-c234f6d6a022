import React, { useCallback } from 'react';
import { useMicAppContext } from 'common/hooks';
import { Instance } from 'common/types';
import { HotPlugModal as HotPlug } from 'pages/instance/list/modals';

interface HotPlugExtraParams {
  instanceId: string;
}

const HotPlugQiankun = () => {
  const { unmountSelf, onOk, onCancel, zone, owner, instanceId } = useMicAppContext<HotPlugExtraParams>();

  const onSuccessCb = useCallback(
    (res: Instance) => {
      onOk?.(res);
      unmountSelf?.();
    },
    [onOk, unmountSelf],
  );

  const onCancelCb = useCallback(() => {
    onCancel?.();
    unmountSelf?.();
  }, [onCancel, unmountSelf]);

  return <HotPlug instanceId={instanceId} owner={owner} zone={zone} onOk={onSuccessCb} onCancel={onCancelCb} />;
};

export default HotPlugQiankun;
