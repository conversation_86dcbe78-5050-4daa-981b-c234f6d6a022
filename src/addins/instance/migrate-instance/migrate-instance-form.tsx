import React from 'react';
import { Select, Alert, portal } from '@pitrix/portal-ui';
import filter from 'lodash-es/filter';
import { t, Instance, Image } from '@pitrix/portal-widget';
import { useController } from 'react-hook-form';

import { Field } from 'common';
import Login<PERSON>ode<PERSON>ield from 'pages/instance/components/login-mode';

interface Props {
  instance: Instance;
}

function MigrateInstanceForm({ instance }: Props): JSX.Element {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const migrateZones = (window?.GLOBAL_CONFIG as any)?.valid_snapshot_migrate_zones ?? [];
  const filtered_zones = filter(window.user.zones, (item) => {
    if (item === window.user.zone) {
      return false;
    }
    return migrateZones.indexOf(item) !== -1;
  });

  const zoneOptions = filtered_zones.map((_zone: string) => {
    const zonesInfo = window.user?.zones_info ?? {};
    return {
      label: zonesInfo?.[_zone]?.['zh-cn'] ?? _zone,
      value: _zone,
    };
  });

  const {
    field: { value: zoneValue, onChange: zoneChange },
  } = useController({
    name: 'dst_zone',
  });
  const {
    field: { onChange: onPasswordChange },
  } = useController({
    name: 'login_passwd',
  });
  const {
    field: { onChange: onKeypairChange },
  } = useController({
    name: 'login_keypair',
  });

  const {
    field: { onChange: onLoginModeChange },
  } = useController({
    name: 'login_mode',
  });

  return (
    <div>
      <Alert
        description={
          <portal.div css={{ color: 'blue.13' }}>
            {t(
              '跨区克隆过程中会对当前虚拟机生成增量备份.',
              'An incremental backup of the current VM is generated during the cross zone cloning process.',
            )}
            <br />
            {t(
              '跨区克隆过程中会生成自定义镜像.',
              'A customized image is generated during the cross-region cloning process.',
            )}
            <br />
            {t(
              '克隆成功的虚拟机需要手动添加网络资源.',
              'Successfully cloned VMs need to manually add network resources.',
            )}
            <br />
            {t(
              '克隆成功的虚拟机会按照按需计费的方式扣费.',
              'Successfully cloned virtual machines will be charged on a pay-as-you-go basis.',
            )}
          </portal.div>
        }
        type="info"
        closable={false}
        css={{ marginBottom: 20, width: '100%' }}
      />
      <Field label={t('选择区域', 'Select zone')}>
        <Select
          width={328}
          placeholder={t('请选择', 'Place select')}
          value={zoneValue}
          options={zoneOptions}
          onChange={zoneChange}
        />
      </Field>
      {zoneValue && (
        <Field label={t('登录方式', 'Login mode')}>
          <div>
            <LoginModeField
              noTitle
              region={zoneValue}
              image={instance?.image as unknown as Image}
              onPasswdChange={onPasswordChange}
              onKeypairChange={onKeypairChange}
              onLoginModeChange={(v) => {
                if (v === 'passwd') {
                  onKeypairChange('');
                } else {
                  onPasswordChange('');
                }
                onLoginModeChange(v);
              }}
            />
          </div>
        </Field>
      )}
    </div>
  );
}

export default MigrateInstanceForm;
