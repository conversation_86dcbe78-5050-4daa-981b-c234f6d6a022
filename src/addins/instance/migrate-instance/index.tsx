import React, { useEffect } from 'react';
import { t, Instance } from '@pitrix/portal-widget';
import NiceModal from '@ebay/nice-modal-react';

import { useMicAppContext } from 'common/hooks';
import { BaseModal, request } from 'common';
import MigrateInstanceForm from './migrate-instance-form';

interface CloneVolumeProps {
  instance: Instance;
}

export default function MigrateInstance(): JSX.Element {
  const { instance, unmountSelf } = useMicAppContext<CloneVolumeProps>();
  const zone = instance?.zone_id || window.user.zone;

  const reg = window?.IAAS?.instance?.login_pwd_reg
    ? new RegExp(window?.IAAS?.instance?.login_pwd_reg)
    : /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!#$%&*.@^_~])[\w!#$%&*.@^~]{8,}$/;

  useEffect(() => {
    if (instance) {
      NiceModal.show('migrate-instance-modal');
    }
  }, [instance]);

  return (
    <BaseModal
      id="migrate-instance-modal"
      name="migrate-instance-modal"
      width={600}
      title={t('跨区克隆云服务器', 'Migrate cloning instance')}
      disabled={(data) => {
        const { dst_zone, login_passwd, login_keypair, login_mode } = data;
        return !(dst_zone && (reg.test(login_passwd) || login_keypair || login_mode === undefined));
      }}
      onCancel={() => {
        unmountSelf?.();
      }}
      onAsyncOk={async (data, close) => {
        await request({
          params: {
            action: 'MigrateResources',
            ...data,
            zone,
            src_zone: window.user.zone,
            resource_type: 'instance',
            resources: [instance?.instance_id],
            owner: window?.user?.user_id,
          },
        }).then((res) => {
          if (res?.ret_code === 0) {
            close?.();
            unmountSelf?.();
          }
        });
      }}
    >
      <MigrateInstanceForm instance={instance} />
    </BaseModal>
  );
}
