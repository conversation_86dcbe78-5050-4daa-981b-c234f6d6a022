import React from 'react';
import { Column, portal } from '@pitrix/portal-ui';
import { t, currencyText } from '@pitrix/portal-widget';

import { InstanceTypeWithPrice } from './model';

function useOptions(billingMode?: string): Column<InstanceTypeWithPrice>[] {
  const columnList: Column<InstanceTypeWithPrice>[] = [
    {
      title: t('规格ID', 'ID'),
      key: 'instance_type_id',
    },
    {
      title: t('vCPUs | 内存', 'vCPU | Memory'),
      key: 'cpu_memory',
      cellRender: (_: unknown, record: InstanceTypeWithPrice) =>
        `${record.vcpus_current || ''}${t('核', 'core')} ｜ ${Math.floor(record.memory_current / 1024) || ''}G`,
    },
  ];

  if (window.GLOBAL_CONFIG?.SYSTEM_SETTINGS?.support_billing) {
    columnList.push({
      title: t('参考价格', 'Price'),
      key: 'price',
      cellRender: (_: unknown, record: InstanceTypeWithPrice) => (
        <portal.div
          css={{
            color: 'brand.default',
          }}
        >
          {record.priceInfo
            ? `${record.priceInfo.price}${currencyText()}/${
                billingMode === 'elastic' ? t('每小时', 'hour') : t('每月', 'month')
              }`
            : '-'}
        </portal.div>
      ),
    });
  }

  return columnList;
}

export default useOptions;
