import React, { ComponentProps, useCallback } from 'react';

import { useMicAppContext } from 'common/hooks';
import { InstanceTypeInfo, OamSelectInstanceTypes } from './oam-select-instance-type';

const OamSelectInstanceTypesModal = () => {
  const { unmountSelf, onSubmit, onClose, ...rest } = useMicAppContext<ComponentProps<typeof OamSelectInstanceTypes>>();

  const onSubmitCb = useCallback(
    (instanceTypes: InstanceTypeInfo[]) => {
      onSubmit?.(instanceTypes);
      unmountSelf?.();
    },
    [onSubmit, unmountSelf],
  );
  const onCloseCb = useCallback(() => {
    onClose?.();
    unmountSelf?.();
  }, [onClose, unmountSelf]);

  return <OamSelectInstanceTypes {...rest} onSubmit={onSubmitCb} onClose={onCloseCb} />;
};

export default OamSelectInstanceTypesModal;
