import React, { useEffect } from 'react';
import { t } from '@pitrix/portal-widget';
import { <PERSON><PERSON><PERSON>ooter, ModalHeader, portal, Table, useModal, Message } from '@pitrix/portal-ui';

import { dispatch, getValues, InstanceTypeWithPrice, useModel } from './model';
import useOptions from './use-options';

export interface InstanceTypeInfo {
  instanceTypeId: string;
  cpu: number;
  memory: number;
}

interface Props {
  zone: string;

  cpuCurrent: number;

  memCurrent: number;

  maxCpu: number;

  maxMem: number;

  /**
   * 主机类型
   */
  instanceClass: number;

  // 主机类型
  instanceStyle?: string;

  /**
   * 已选择的规格id
   */
  selectedIds: string[];

  /**
   * 主机id （用于查询计费信息）
   */
  instanceId: string;

  imageId: string;

  onSubmit: (instanceTypes: InstanceTypeInfo[]) => void;

  maxSelectLength?: number;

  onClose: () => void;
}

export const OamSelectInstanceTypes = ({
  zone,
  maxCpu,
  maxMem,
  instanceClass,
  instanceStyle,
  instanceId,
  onSubmit,
  selectedIds,
  cpuCurrent,
  memCurrent,
  imageId,
  maxSelectLength,
  onClose,
}: Props) => {
  const { Modal, close } = useModal({ defaultOpen: true, onClose });

  const loading = useModel('loading');

  const instanceTypes = useModel('instanceTypes');

  const selectedInstanceTypes = useModel('selectedTypes');

  const billingMode = useModel('billingMode');

  useEffect(() => {
    dispatch({
      type: 'init',
      payload: {
        zone,
        instanceClass,
        cpuCurrent,
        memCurrent,
        maxCpu,
        maxMem,
        instanceId,
        imageId,
        instanceStyle,
      },
    });
  }, [cpuCurrent, imageId, instanceClass, instanceId, maxCpu, maxMem, memCurrent, zone, instanceStyle]);

  useEffect(() => {
    dispatch({ type: 'mount', payload: undefined });
  }, []);

  useEffect(() => {
    if (maxSelectLength && maxSelectLength < getValues().selectedTypes.length) {
      Message.error({ message: t(`最多只能选择${maxSelectLength}个`, 'At most ${maxSelectLength} can be selected') });
    }
  }, [maxSelectLength, getValues().selectedTypes]);

  const columns = useOptions(billingMode);

  return (
    <Modal>
      <ModalHeader title={t('选择扩容规格', 'Select Instance Types')} />
      <portal.div css={{ minWidth: 480, maxHeight: 400, overflowY: 'auto' }}>
        <Table
          loading={loading}
          selectionType="multiple"
          selectedRowKeys={selectedInstanceTypes.map((_t) => _t.instance_type_id) as (keyof InstanceTypeWithPrice)[]}
          onSelectionChange={(_, types) => dispatch({ type: 'select', payload: types })}
          disabledRowKeys={selectedIds as (keyof InstanceTypeWithPrice)[]}
          dataSource={instanceTypes}
          rowKey="instance_type_id"
          css={{
            width: '100%',
            '& table': { margin: 0 },
            '& .description': {
              textAlign: 'center',
            },
          }}
          columns={columns}
        />
      </portal.div>
      <ModalFooter
        btns={[
          {
            key: 'cancel',
            children: t('取消', 'Cancel'),
            onClick: () => {
              close();
              onClose();
            },
          },
          {
            key: 'ok',
            children: t('确定', 'Submit'),
            disabled: (maxSelectLength || getValues().selectedTypes.length) < getValues().selectedTypes.length,
            type: 'primary',
            onClick: () => {
              onSubmit(
                getValues().selectedTypes.map((_t) => ({
                  instanceTypeId: _t.instance_type_id,
                  cpu: _t.vcpus_current,
                  memory: _t.memory_current,
                })),
              );
              close();
              onClose();
            },
          },
        ]}
      />
    </Modal>
  );
};
