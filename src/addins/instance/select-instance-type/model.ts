import {
  GetPriceResponse,
  fromRequestDescribeInstanceTypes,
  fromRequestGetLeaseInfo,
  GetLeaseInfoResponse,
  fromRequestGetInstancePrice,
} from '@pitrix/portal-widget';
import { forkJoin, map, of } from 'rxjs';

import { InstanceType, createModel } from '../../../common';

export interface InstanceTypeWithPrice extends InstanceType {
  priceInfo?: GetPriceResponse['price_set'][number];
}

interface Model {
  instanceTypes: InstanceTypeWithPrice[];
  loading: boolean;
  selectedTypes: InstanceTypeWithPrice[];
  billingMode: string | undefined;
}

interface InitProps {
  // 主机的zone
  zone: string;

  // 当前cpu
  cpuCurrent: number;

  // 当前内存
  memCurrent: number;

  // 最大cpu
  maxCpu: number;

  // 最大内存
  maxMem: number;

  // 主机类型
  instanceClass: number;

  // 主机id （用于查询计费信息）
  instanceId: string;

  // 主机类型
  instanceStyle?: string;

  // 主机镜像id
  imageId: string;
}

const computeSequence = (instanceTypeID = '') => {
  if (instanceTypeID.length === 0) return 0;
  return instanceTypeID.split('').reduce((pre, cur) => {
    // eslint-disable-next-line no-bitwise
    const res: number = (pre << 5) - pre + cur.charCodeAt(0);

    return Math.abs(res || 0);
  }, 0);
};

export const { dispatch, useModel, getValues } = createModel<Model>({
  instanceTypes: [],
  loading: false,
  selectedTypes: [],
  billingMode: undefined,
})({
  /**
   * 初始化数据
   * @param props
   * @returns
   */
  init:
    (props: InitProps) =>
    ({ loading$, instanceTypes$, billingMode$ }) => {
      const { zone, instanceClass, cpuCurrent, instanceStyle, memCurrent, maxCpu, maxMem, instanceId, imageId } = props;
      loading$.next(true);
      forkJoin({
        instanceTypes: fromRequestDescribeInstanceTypes({
          zone,
          owner: window.user.user_id,
          resource_type: 'instance',
          status: ['available'],
          resource_class: instanceClass,
          offset: 0,
          limit: 200,
          instance_style: instanceStyle,
        }),
        leaseInfo: window.GLOBAL_CONFIG?.SYSTEM_SETTINGS?.support_billing
          ? fromRequestGetLeaseInfo({
              resources: [instanceId],
              resource: instanceId,
              verbose: 2,
              zone,
              owner: window.user.user_id,
            })
          : of({} as GetLeaseInfoResponse),
      })
        .pipe(
          map((res) => {
            let billingMode: string | undefined;
            if (res.leaseInfo) {
              billingMode =
                // @ts-ignore
                // TODO 此处查询单条数据和列表数据格式不统一
                (res.leaseInfo?.lease_info_set?.[0]?.contract?.charge_mode ||
                  res.leaseInfo.lease_info?.contract?.charge_mode) === 'elastic'
                  ? 'elastic'
                  : 'monthly';
            }
            return {
              instanceTypes: res.instanceTypes.instance_type_set || [],
              billingMode,
            };
          }),
          map(({ instanceTypes, billingMode }) => {
            return {
              instanceTypes: instanceTypes
                .filter(
                  ({ vcpus_current, memory_current, instance_class_feature }) =>
                    // 1、vcpus_current 和 memory_current 不能超过最大
                    // 2、满足有其中一个配置大于当前配置，且不低于当前配置
                    ((vcpus_current === cpuCurrent && memory_current > memCurrent) || vcpus_current > cpuCurrent) &&
                    vcpus_current <= maxCpu &&
                    memory_current <= maxMem,
                )
                .sort((a, b) => {
                  return a.vcpus_current === b.vcpus_current
                    ? Number(a.memory_current) - Number(b.memory_current)
                    : Number(a.vcpus_current) - Number(b.vcpus_current);
                }),
              billingMode,
            };
          }),
        )
        .subscribe(({ instanceTypes, billingMode }) => {
          loading$.next(false);
          billingMode$.next(billingMode);
          instanceTypes$.next(instanceTypes);
          if (billingMode && !!instanceTypes.length) {
            fromRequestGetInstancePrice({
              zone,
              currency: window.__CURRENCY,
              owner: window.user.user_id,
              resources: instanceTypes.map((type) => ({
                type: 'instance',
                cpu: type.vcpus_current,
                memory: type.memory_current,
                instance_class: type.instance_class,
                instance_type: type.instance_type_id,
                image_id: imageId,
                image_size: 0,
                instance_class_feature: type.instance_class_feature,
                sequence: computeSequence(type.instance_type_id),
              })),
            })
              .pipe(map((res) => res.price_set || []))
              .subscribe((priceSet) => {
                instanceTypes$.next(
                  instanceTypes$.value.map((type) => {
                    const priceInfo = priceSet.find((p) => p.sequence === computeSequence(type.instance_type_id));
                    return {
                      ...type,
                      priceInfo,
                    };
                  }),
                );
              });
          }
        });
    },
  /**
   * 选择规格
   * @param types
   * @returns
   */
  select:
    (types: InstanceTypeWithPrice[]) =>
    ({ selectedTypes$ }) => {
      selectedTypes$.next(types);
    },
  /**
   * 卸载数据
   * @returns
   */
  mount:
    () =>
    ({ instanceTypes$, loading$, selectedTypes$, billingMode$ }) => {
      instanceTypes$.next([]);
      loading$.next(false);
      selectedTypes$.next([]);
      billingMode$.next(undefined);
    },
});
