import React, { useMemo } from 'react';
import NiceModal from '@ebay/nice-modal-react';
import { Loading } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { get } from 'lodash-es';

import { ConfirmContent, BaseModal, request, Instance } from 'common';
import { useMicAppContext, useRequest } from 'common/hooks';

interface MicAppProps {
  instance: Instance;
}

const ID = 'MacvtapMulticast';

function MacvtapMulticast(): JSX.Element {
  const { unmountSelf, instance, cb } = useMicAppContext<MicAppProps>();

  const { data, isFetching } = useRequest(
    {
      action: 'DescribeInstances',
      zone: instance?.zone_id || window.user.zone,
      owner: window.user.user_id,
      instances: [instance?.instance_id],
      status: [],
    },
    {
      enabled: !!instance.instance_id,
    },
  );

  const isOpen = useMemo(() => {
    const curInstance = data?.instance_set?.[0];
    return !!get(curInstance?.extra, 'trust_guest_rx_filters', 0);
  }, [data]);

  const onSubmit = async () => {
    const res = await request({
      params: {
        action: 'ModifyInstanceAttributes',
        instance: instance?.instance_id,
        zone: window?.user?.zone,
        owner: window?.user?.user_id,
        trust_guest_rx_filters: isOpen ? 0 : 1,
      },
    });
    if (res && res.ret_code === 0) {
      unmountSelf?.();
      cb?.(res);
    }
  };

  React.useEffect(() => {
    if (instance) {
      NiceModal.show(ID);
    }
  }, [instance]);

  return (
    <BaseModal
      width={500}
      id={ID}
      name={ID}
      title=""
      okType={isOpen ? 'danger' : 'primary'}
      okText={isOpen ? t('关闭', 'Close') : t('开启', 'Enable')}
      onAsyncOk={onSubmit}
      onCancel={() => {
        unmountSelf?.();
      }}
      bordered={false}
      disabled={isFetching}
    >
      {isFetching ? (
        <Loading size="s" />
      ) : (
        <ConfirmContent
          type={isOpen ? 'error' : 'info'}
          title={
            isOpen
              ? t(
                  `确定要关闭 "${instance?.instance_id ?? ''}" 的 MacVTap 网络信任吗？`,
                  `Sure you want to turn off the "${instance?.instance_id ?? ''}"for MacVTap network trust?`,
                )
              : t(
                  `确定要开启 "${instance?.instance_id ?? ''}" 的 MacVTap 网络信任吗？`,
                  `Sure you want to enable the "${instance?.instance_id ?? ''}" for MacVTap network trust?`,
                )
          }
        />
      )}
    </BaseModal>
  );
}

export default MacvtapMulticast;
