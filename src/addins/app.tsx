import React, { Suspense } from 'react';
import NiceModal from '@ebay/nice-modal-react';

import { useMicAppContext } from 'common/hooks';
import { FullLoading, IaasProvider } from '../common';
import ADDINS_MAPPING from './mapping';

function Addin({ id, ...props }: { id: string }): JSX.Element {
  return <Suspense fallback={null}>{React.createElement(ADDINS_MAPPING[id], props)}</Suspense>;
}

function App() {
  const { id } = useMicAppContext<{ id: string }>();

  return (
    <NiceModal.Provider>
      <IaasProvider fullback={<FullLoading />} rootID="iaas-addins-container">
        <Addin id={id} />
      </IaasProvider>
    </NiceModal.Provider>
  );
}

export default App;
