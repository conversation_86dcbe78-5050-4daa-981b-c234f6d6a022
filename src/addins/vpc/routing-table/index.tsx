import React, { useMemo } from 'react';

import { t } from '@pitrix/portal-widget';
import { portal, Table, Button, Input, RadioButtonGroup } from '@pitrix/portal-ui';
import { useAtom } from 'jotai';

import { useMicAppContext, useRequest } from 'common/hooks';
import { limitAtom, offsetAtom, searchAtom, ipVersionAtom } from './state';

const { div: Div } = portal;
function RoutingTable(): JSX.Element {
  const { model } = useMicAppContext<any>();
  const [limit, setLimit] = useAtom(limitAtom);
  const [offset, setOffset] = useAtom(offsetAtom);
  const [search, setSearch] = useAtom(searchAtom);
  const [ipVersion, setIpVersion] = useAtom(ipVersionAtom);

  const columns = [
    {
      title: t('目标网络', 'Target Network'),
      dataIndex: 'Destination',
      key: 'Destination',
    },
    {
      title: t('下一跳', 'Next Hop'),
      dataIndex: 'Nexthop',
      key: 'Nexthop',
    },
    {
      title: t('协议', 'Protocols'),
      dataIndex: 'Proto',
      key: 'Proto',
    },
    {
      title: t('权重', 'Metric'),
      dataIndex: 'Metric',
      key: 'Metric',
    },
  ];

  const { data, isFetching, refetch } = useRequest(
    {
      action: 'DescribeBorderRoute',
      border: model.id,
      ip_version: ipVersion,
      search_word: search,
    },
    {
      enabled: !!model?.id,
      keepPreviousData: true,
    },
  );

  const dataResource = useMemo(() => {
    const routers = data?.routes || [];
    return routers.slice(offset, offset + limit);
  }, [data, limit, offset]);

  return (
    <Div>
      <Div
        css={{ display: 'flex', alignItems: 'center', gap: '8px', justifyContent: 'space-between', marginBottom: 12 }}
      >
        <Div css={{ display: 'flex', gap: '8px' }}>
          <Input
            clearable
            prefix="MagnifierFill"
            placeholder={t('输入目标网络或下一跳搜索', 'Enter the target network or next-hop search')}
            width={272}
            onClear={() => {
              setSearch('');
            }}
            onPressEnter={(e) => {
              setSearch((e as unknown as React.ChangeEvent<HTMLInputElement>)?.target?.value);
            }}
          />
          <Button icon="refresh_2_fill" onClick={() => refetch()} />
        </Div>
        <RadioButtonGroup
          value={ipVersion}
          onChange={setIpVersion}
          options={[
            { label: 'IPv4', value: '4' },
            { label: 'IPv6', value: '6' },
          ]}
        />
      </Div>
      <Table
        rowKey="as_number"
        loading={isFetching}
        dataSource={dataResource}
        columns={columns}
        pagination={{
          showQuickJumper: true,
          showTotal: true,
          showSizeChanger: true,
          pageSize: limit,
          current: (offset + limit) / limit,
          total: data?.total_count ?? 0,
          onPaging: (page, pageSize) => {
            setOffset((page - 1) * pageSize);
            setLimit(pageSize);
          },
        }}
      />
    </Div>
  );
}

export default RoutingTable;
