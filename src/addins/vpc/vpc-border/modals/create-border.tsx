import React, { forwardRef, useImperativeHandle } from 'react';
import { t } from '@pitrix/portal-widget';
import { Input, portal } from '@pitrix/portal-ui';
import { useController } from 'react-hook-form';

import { FieldItem } from 'common';
import { useSubmit } from 'common/hooks';

const CreateBorder = forwardRef(({ data }, ref) => {
  const { mutate } = useSubmit();

  const {
    field: { value, onChange },
  } = useController({
    name: 'border_name',
  });

  const onSubmit = async (params: Record<string, unknown>, hide: () => void) => {
    await new Promise((resolve, reject) => {
      mutate(
        {
          ...params,
          action: 'CreateVpcBorders',
          border: data?.vpc_border_id,
          zone: window.user.zone,
        },
        {
          onSuccess: (v) => {
            resolve?.(v);
            hide?.();
          },
          onError: (err) => {
            reject?.(err);
          },
        },
      );
    });
  };

  useImperativeHandle(ref, () => ({
    submit: onSubmit,
  }));

  return (
    <div>
      <portal.div css={{ padding: '0 16px' }}>
        <FieldItem label={t('名称', 'Name')}>
          <Input value={value} autoComplete="new-password" onChange={onChange} />
        </FieldItem>
      </portal.div>
    </div>
  );
});

export default CreateBorder;
