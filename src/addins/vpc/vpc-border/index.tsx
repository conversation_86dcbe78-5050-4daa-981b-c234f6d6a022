import React from 'react';
import { t } from '@pitrix/portal-widget';
import { Table, portal, Button, Icon } from '@pitrix/portal-ui';

const { div: Div } = portal;
function VpcBorder() {
  const columns = [
    {
      title: t('名称 / ID', 'Name / ID'),
      dataIndex: 'as_number',
      key: 'as_number',
    },
    {
      title: 'zhuangtai',
      dataIndex: 'State',
      key: 'State',
    },
    {
      title: t('区域', 'Zone'),
      dataIndex: 'Up/Down',
      key: 'Up/Down',
    },
    {
      title: t('创建时间', 'Create Time'),
      dataIndex: 'create_time',
      key: 'create_time',
    },
    {
      title: t('操作', 'Operation'),
      dataIndex: 'status23',
      key: 'status3',
      cellRender: (_, record) => (
        <portal.div css={{ display: 'flex' }}>
          <portal.a
            css={{
              marginRight: 16,
            }}
            onClick={() => {}}
          >
            {t('解绑', 'Unbundle')}
          </portal.a>
        </portal.div>
      ),
    },
  ];
  return (
    <Div>
      <Div css={{ display: 'flex', gap: '12px', marginBottom: 15 }}>
        <Button type="primary">
          <Icon name="add_fill" style={{ marginRight: 6 }} />
          {t('创建', 'Create')}
        </Button>
        <Button>
          <Icon name="chain_fill" style={{ marginRight: 6 }} />
          {t('绑定', 'Bind')}
        </Button>
      </Div>
      <Table rowKey="aaa" dataSource={[]} columns={columns} />
    </Div>
  );
}

export default VpcBorder;
