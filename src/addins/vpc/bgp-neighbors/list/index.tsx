import React from 'react';
import { t } from '@pitrix/portal-widget';
import { portal, Column } from '@pitrix/portal-ui';
import { useAtomValue } from 'jotai';
import { useInterval } from 'react-use';

import Table from 'common/components/table';
import { useRequest } from 'common/hooks';
import { refreshIntervalAtom, paramsAtom, borderIdAtom } from '../state';
import { handleDeleteBGP, handleDisableBGP, handleEnableBGP, handleModifyBGP } from '../modals';
import { Bgp } from '../types';
import Status from './status';

function List(): JSX.Element {
  const params = useAtomValue(paramsAtom);
  const time = useAtomValue(refreshIntervalAtom);
  const borderID = useAtomValue(borderIdAtom);

  const { data, isFetching, refetch } = useRequest(
    {
      action: 'DescribeBorderBgpPeer',
      border: borderID,
      ...params,
    },
    {
      enabled: !!borderID,
      keepPreviousData: true,
    },
  );

  const columns: Column<Bgp>[] = [
    {
      title: t('BGP 邻居 IP', 'BGP Neighbor IP'),
      dataIndex: 'peer_ip',
      key: 'peer_ip',
    },
    {
      title: t('状态', 'Status'),
      dataIndex: 'State',
      key: 'State',
      cellRender: (v) => <Status status={((v as string) || '').toLowerCase()} />,
    },
    {
      title: t('AS 号', 'AS Number'),
      dataIndex: 'as_number',
      key: 'as_number',
    },
    {
      title: t('存活时间（秒）', 'Hold Time (sec)'),
      dataIndex: 'keepalive',
      key: 'keepalive',
    },
    {
      title: t('保持时间（秒）', ' Keep Alive Time (sec)'),
      dataIndex: 'hold_time',
      key: 'hold_time',
    },
    {
      title: t('操作', 'Operation'),
      dataIndex: 'status',
      key: 'status',
      width: 200,
      cellRender: (status, record) => (
        <portal.div css={{ display: 'flex' }}>
          {status === '' ? (
            <portal.a
              css={{
                marginRight: 16,
              }}
              onClick={() => handleDisableBGP({ ...record, ip_version: params.ip_version, border: borderID })}
            >
              {t('禁用', 'Disable')}
            </portal.a>
          ) : (
            <portal.a
              css={{
                marginRight: 16,
              }}
              onClick={() => handleEnableBGP({ ...record, ip_version: params.ip_version, border: borderID })}
            >
              {t('启用', 'Enable')}
            </portal.a>
          )}

          <portal.a
            css={{
              marginRight: 16,
            }}
            onClick={() => handleModifyBGP({ ...record, ip_version: params.ip_version, border: borderID })}
          >
            {t('修改', 'Modify')}
          </portal.a>

          <portal.a
            css={{
              marginRight: 16,
            }}
            onClick={() => handleDeleteBGP({ ...record, ip_version: params.ip_version, border: borderID })}
          >
            {t('删除', 'Delete')}
          </portal.a>
        </portal.div>
      ),
    },
  ];

  useInterval(
    () => {
      refetch();
    },
    +time ? +time * 1000 : null,
  );

  return (
    <Table
      rowKey="as_number"
      loading={isFetching}
      dataSource={data?.peer_list ?? []}
      columns={columns}
      jobActions={['ModifyBorderBgpPeer', 'AddBorderBgpPeer', 'DelBorderBgpPeer']}
      jobRefresh={refetch}
    />
  );
}

export default List;
