import React from 'react';
import { ColorString, portal } from '@pitrix/portal-ui';
import { keyframes } from '@emotion/react';
import { t } from '@pitrix/portal-widget';

const BLUE = '#1286F1';
const GREEN = '#15A675';
const GRAY = '#939EA9';
const RED = '#CF3B37';

const map: {
  [name: string]: {
    color: string;
    text: string;
    isKeyframe?: boolean;
  };
} = {
  connect: {
    color: BLUE,
    text: t('连接中', 'Connecting'),
    isKeyframe: true,
  },
  idle: {
    color: BLUE,
    text: t('空闲', 'Idle'),
  },
  established: {
    color: GREEN,
    text: t('已建立', 'Established'),
  },
  opensent: {
    color: BLUE,
    text: t('打开发送', 'Opensent'),
  },
  openconfirm: {
    color: BLUE,
    text: t('打开确认', 'Cpenconfirm'),
  },
  active: {
    color: BLUE,
    text: t('激活', 'Active'),
  },
};

const keyframe = keyframes`
  0% {
    transform: scale(1,1);
    opacity: 1;
  }
  50% {
    transform: scale(0.8,0.8);
    opacity: 0.5;
  }
  100% {
    transform: scale(1,1);
    opacity: 1;
  }
`;
type StatusKeys = keyof typeof map;

const Status = ({ status }: { status: string }) => {
  const color = (map?.[status as StatusKeys]?.color || map?.pending?.color) as ColorString;

  const text = map?.[status as StatusKeys]?.text || status;

  const isKeyframe = map?.[status as StatusKeys]?.isKeyframe;

  return (
    <portal.div css={{ display: 'flex', alignItems: 'center' }}>
      <portal.div
        css={{
          width: 12,
          height: 12,
          marginRight: 6,
          center: true,
        }}
      >
        <portal.div
          css={{
            center: true,
            width: 12,
            height: 12,
            borderRadius: 'full',
            backgroundColor: `${color}26` as ColorString,
            animation: !!isKeyframe && `2s ease 0s infinite running ${keyframe}`,
          }}
        >
          <portal.div
            css={{
              center: true,
              width: 8,
              height: 8,
              borderRadius: 'full',
              backgroundColor: `${color}76` as ColorString,
            }}
          >
            <portal.div
              css={{
                width: 6,
                height: 6,
                borderRadius: 'full',
                backgroundColor: color,
              }}
            />
          </portal.div>
        </portal.div>
      </portal.div>
      <portal.div css={{ display: 'flex', alignItems: 'center' }}>
        <portal.div css={{ textBody: 's', color }}>{text}</portal.div>
      </portal.div>
    </portal.div>
  );
};

export default Status;
