import React from 'react';
import { FilterCell } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useAtom } from 'jotai';

import { statusAtom } from '../state';

function StatusFilter() {
  const [value, setValue] = useAtom(statusAtom);

  const options = [
    { label: t('全部', 'All'), value: 'ALL' },
    { value: '已建立', label: '已建立' },
    { value: '空闲', label: '空闲' },
    { value: '连接中', label: '连接中' },
  ];

  const handleChange = (v: string) => {
    setValue(v);
  };

  return (
    <FilterCell value={value} onChange={handleChange as () => void} filters={options}>
      {t('状态', 'Status')}
    </FilterCell>
  );
}

export default StatusFilter;
