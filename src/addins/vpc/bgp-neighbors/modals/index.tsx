import React, { createRef } from 'react';
import { Modal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';

import { IaasModal, request } from 'common';

import AddBgp from './add-bgp';
import { Bgp } from '../types';

const getRoot = () => document.getElementById('iaas-addins-container');

export const handleAddBGP = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const ref = createRef<any>();
  IaasModal.create({
    title: t('添加 BGP 邻居', 'Add BGP Neighbor'),
    width: 600,
    name: 'add-bgp',
    children: <AddBgp ref={ref} />,
    onAsyncOk: async (data, hide) => {
      await ref?.current?.submit(data, hide);
    },
  });
};

export const handleModifyBGP = (props: Bgp) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const ref = createRef<any>();
  IaasModal.create({
    title: t('修改 BGP 邻居', 'Modify BGP Neighbor'),
    width: 600,
    name: 'modify-bgp',
    children: <AddBgp ref={ref} data={props} />,
    onAsyncOk: async (data, hide) => {
      await ref?.current?.submit(data, hide);
    },
  });
};

export const handleDeleteBGP = (props: Bgp) => {
  Modal.alert({
    root: getRoot(),
    width: 500,
    okText: t('删除', 'Delete'),
    cancelText: t('取消', 'Cancel'),
    title: t(`确定要删除 BGP 邻居“${props?.peer_ip}”?`, `Identify the BGP neighbor "${props?.peer_ip}" to be deleted?`),
    content: t(
      '删除后，部分路由将失效，可能导致网络无法访问。请谨慎操作。',
      'After deletion, some of the routes will be invalidated, which may cause the network to be inaccessible. Please operate with caution.',
    ),
    onOk: async () => {
      await request({
        params: {
          action: 'DelBorderBgpPeer',
          border: props?.border,
          ip_version: props?.ip_version,
          address: props?.peer_ip,
          zone: window?.user.zone,
        },
      });
    },
  });
};

export const handleEnableBGP = (props: Bgp) => {
  Modal.warning({
    root: getRoot(),
    width: 500,
    okText: t('启用', 'Enable'),
    cancelText: t('取消', 'Cancel'),
    title: t(
      `确定要启用 BGP 邻居“${props?.peer_ip}”?`,
      `Make sure you want to enable the BGP neighbor “${props?.peer_ip}”?`,
    ),
    onOk: async () => {
      await request({
        params: {
          action: 'ModifyBorderBgpPeer',
          border: props?.border,
          ip_version: props?.ip_version,
          address: props?.peer_ip,
          zone: window?.user.zone,
          status: 'enable' as unknown as string[],
        },
      });
    },
  });
};

export const handleDisableBGP = (props: Bgp) => {
  Modal.alert({
    root: getRoot(),
    width: 500,
    okText: t('禁用', 'Disable'),
    cancelText: t('取消', 'Cancel'),
    title: t(`确定要禁用 BGP 邻居“${props?.peer_ip}”?`, `Make sure to disable the BGP neighbor "${props?.peer_ip}"`),
    content: t(
      '禁用后，可能导致网络无法访问。请谨慎操作。',
      'Disabling it may cause the network to be inaccessible. Please operate with caution.',
    ),
    onOk: async () => {
      await request({
        params: {
          action: 'ModifyBorderBgpPeer',
          border: props?.border,
          ip_version: props?.ip_version,
          address: props?.peer_ip,
          zone: window?.user.zone,
          status: 'disable' as unknown as string[],
        },
      });
    },
  });
};
