import React, { forwardRef, useImperative<PERSON>andle, useMemo } from 'react';
import { t } from '@pitrix/portal-widget';
import { Input, portal, RadioGroup, InputNumber } from '@pitrix/portal-ui';
import { useController, useFormState } from 'react-hook-form';
import { useAtom } from 'jotai';

import { FieldItem } from 'common';
import { useSubmit } from 'common/hooks';
import { baseInfoAtom, borderoAtom } from '../state';
import { Bgp } from '../types';

const reg = /^(?=.*[A-Z])(?=.*[a-z])|(?=.*[A-Z])(?=.*\d)|(?=.*[a-z])(?=.*\d)[A-Za-z\d]{8,32}$/;
const ipv4Reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
const ipv6Reg =
  /^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/;

const AddBgp = forwardRef(({ data }: { data?: Bgp }, ref) => {
  const [baseInfo] = useAtom(baseInfoAtom);
  const [border] = useAtom(borderoAtom);

  const { mutate } = useSubmit();

  const { errors } = useFormState();

  const {
    field: { value: asValue, onChange: asChange },
  } = useController({
    name: 'as_number',
    rules: {
      required: true,
    },
    defaultValue: data?.as_number ?? '',
  });

  const {
    field: { value: ipVersion, onChange: ipChange },
  } = useController({
    name: 'ip_version',
    defaultValue: data?.ip_version ?? '4',
  });

  const {
    field: { value: keepalive, onChange: keepliveChange },
  } = useController({
    name: 'keepalive',
    rules: {
      required: true,
      min: 6,
      max: 900,
      validate: (v, s) => {
        return s.hold_time * 3 === v;
      },
    },
    defaultValue: data?.keepalive ?? 6,
  });

  const {
    field: { value: holdTime, onChange: holdTimeChange },
  } = useController({
    name: 'hold_time',
    defaultValue: data?.hold_time ?? 2,
    rules: {
      required: true,
      min: 2,
      max: 300,
    },
  });

  const {
    field: { value: address, onChange: addressChange },
  } = useController({
    name: 'address',
    rules: {
      required: true,
      validate: (v) => {
        if (data) {
          return true;
        }
        if (ipVersion === '4') {
          return ipv4Reg.test(v);
        }
        return ipv6Reg.test(v);
      },
    },
    defaultValue: data?.peer_ip ?? '',
  });

  const {
    field: { value: password, onChange: padChange },
  } = useController({
    name: 'password',
    rules: {
      validate: (v) => {
        if (!v) return true;
        return reg.test(v);
      },
    },
  });

  const keepaliveErr = () => {
    const keep = errors?.keepalive;
    if (keep) {
      switch (keep.type) {
        case 'validate':
          return t('存活时间为保持时间的3倍', 'Survival time is 3 times the holding time');
        case 'required':
          return t('请输入存活时间', 'Please enter the survival time');
        default:
          return t('范围：6～900。', 'Range: 6~900.');
      }
    }
    return '';
  };

  const holdTimeErr = () => {
    const hold = errors?.hold_time;
    if (hold) {
      switch (hold.type) {
        case 'required':
          return t('请输入保持时间', 'Please enter the hold time');
        default:
          return t('范围：2～300。', 'Range: 6~900.');
      }
    }
    return '';
  };

  const onSubmit = async (params: Record<string, unknown>, hide: () => void) => {
    await new Promise((resolve, reject) => {
      if (!params?.password) {
        params.password = undefined;
      }
      mutate(
        {
          ...params,
          action: data ? 'ModifyBorderBgpPeer' : 'AddBorderBgpPeer',
          border: border?.vpc_border_id,
          zone: window.user.zone,
        },
        {
          onSuccess: (v) => {
            resolve?.(v);
            hide?.();
          },
          onError: (err) => {
            reject?.(err);
          },
        },
      );
    });
  };

  useImperativeHandle(ref, () => ({
    submit: onSubmit,
  }));

  return (
    <div>
      <portal.div css={{ padding: '0 16px' }}>
        <FieldItem label={t('本地 AS', 'Local AS')}>{baseInfo?.as_num ?? '--'}</FieldItem>
        <FieldItem label={t('IP 版本', 'IP Version')}>
          <RadioGroup
            value={ipVersion}
            onChange={ipChange}
            disabled={!!data}
            options={[
              {
                label: 'IPv4',
                value: '4',
              },
              {
                label: 'IPv6',
                value: '6',
              },
            ]}
          />
        </FieldItem>
        <FieldItem
          label={t('邻居 AS', 'Neighborhood AS')}
          error={errors?.as_number ? t('请输入邻居 AS', 'Please enter the neighbor AS') : ''}
        >
          <Input
            value={asValue}
            autoComplete="new-password"
            onChange={asChange}
            disabled={!!data}
            error={!!errors?.as_number}
            type={'number' as 'text'}
          />
        </FieldItem>
        <FieldItem
          label={t('邻居地址', 'Neighborhood Address')}
          error={
            errors?.address ? t(`请输入合法的IPv${ipVersion}地址`, `Please enter a legal IPv${ipVersion} address`) : ''
          }
        >
          <Input
            value={address}
            autoComplete="new-password"
            onChange={addressChange}
            disabled={!!data}
            error={!!errors?.address}
          />
        </FieldItem>
        <FieldItem
          label={t('存活时间', 'Survival time')}
          help={t(
            '范围：6～900。存活时间一般为保持时间的3倍',
            'Range: 6~900.Survival time typically 3 times holding time',
          )}
          error={keepaliveErr()}
        >
          <InputNumber
            max={900}
            controls="none"
            unit={t('秒', 'second')}
            value={keepalive}
            onChange={keepliveChange}
            error={!!errors.keepalive}
          />
        </FieldItem>
        <FieldItem
          label={t('保持时间', 'Holding time')}
          help={t('范围：2～300。', 'Range: 2~300.')}
          error={holdTimeErr()}
        >
          <InputNumber
            max={300}
            unit={t('秒', 'second')}
            controls="none"
            value={holdTime}
            onChange={holdTimeChange}
            error={!!errors.hold_time}
          />
        </FieldItem>
        <FieldItem
          error={
            errors?.password
              ? t(
                  '密码长度范围为 8 ～ 32 位,必须包含大写字母、小写字母、数字中的任意两种。',
                  'the password length range is 8 ~ 32 characters and must contain any two of uppercase letters lowercase letters or numbers.',
                )
              : ''
          }
          label={
            <>
              {t('密码', 'Password')}
              <portal.span css={{ color: 'text.placeholder' }}>{`(${t('选填', 'optional')})`}</portal.span>
            </>
          }
          help={t(
            '密码长度范围为 8 ～ 32 位,必须包含大写字母、小写字母、数字中的至少任意两种。',
            'The password length must be between 8 and 32 characters and must include at least two of the following: uppercase letters, lowercase letters, or numbers.',
          )}
        >
          <portal.div css={{ position: 'relative' }}>
            <Input
              autoComplete="new-password"
              width={300}
              value={password}
              onChange={padChange}
              max={32}
              style={{
                position: 'absolute',
                left: 0,
                top: 0,
                WebkitTextFillColor: 'transparent',
                borderColor: 'transparent',
                background: 'none',
              }}
            />
            <Input
              type="password"
              autoComplete="new-password"
              visibilityToggle
              width={328}
              value={password}
              onChange={padChange}
              max={32}
            />
          </portal.div>
        </FieldItem>
      </portal.div>
    </div>
  );
});

export default AddBgp;
