export interface Border {
  border_ip_addr?: string;
  enable_ipv6?: number;
  place_group_id?: string;
  controller?: string;
  vpc_border_id?: string;
  border_type?: number;
  border_name?: string;
  bgp_subif_idx?: string;
}

export interface BaseInfo {
  as_num?: string;
  loading?: boolean;
  router_id?: string;
}

export interface Bgp {
  as_number?: string;
  loading?: boolean;
  router_id?: string;
  State?: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'Up/Down'?: string;
  peer_ip?: string;
  ip_version?: string;
  status?: string;
  border?: string;
  hold_time?: number;
  keepalive?: number;
}
