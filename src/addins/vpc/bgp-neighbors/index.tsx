import React, { useEffect } from 'react';
import { t } from '@pitrix/portal-widget';
import { portal, Skeleton } from '@pitrix/portal-ui';
import { useAtom } from 'jotai';

import { useMicAppContext, useRequest } from 'common/hooks';
import Toolbar from './toolbar';
import List from './list';
import { baseInfoAtom, borderoAtom } from './state';
import { Border } from './types';

const { div: Div, span: Span } = portal;

function BGPneighbors(): JSX.Element {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { model } = useMicAppContext<{ model: Border }>();
  const [, setBaseInfo] = useAtom(baseInfoAtom);
  const [, setBorder] = useAtom(borderoAtom);

  const { data, isLoading } = useRequest(
    {
      action: 'DescribeBorderBgpInfo',
      border: model.vpc_border_id,
    },
    {
      enabled: !!model.vpc_border_id,
      keepPreviousData: true,
    },
  );

  useEffect(() => {
    setBaseInfo({
      ...data,
      loading: isLoading,
    });
    return () => {
      setBaseInfo({});
    };
  }, [isLoading, data, setBaseInfo]);

  useEffect(() => {
    if (model) {
      setBorder(model);
    }
  }, [model, setBorder]);

  return (
    <Div>
      <Div
        css={{
          backgroundColor: 'fill.deep',
          padding: '12px 16px',
        }}
      >
        <Div css={{ lineHeight: '24px', fontSize: '14px', fontWeight: '500', marginBottom: 8 }}>
          {t('BGP 基本信息', 'BGP Basic Information')}
        </Div>
        <Div css={{ lineHeight: '20px', display: 'flex', gap: '20%' }}>
          <Div css={{ center: true }}>
            <Span css={{ color: 'text.placeholder', marginRight: 20 }}>{t('本端 AS 号', 'Local AS number')}</Span>
            {isLoading ? <Skeleton block active width={100} height={15} /> : <Span>{data?.as_num ?? '--'}</Span>}
          </Div>
          {/* <Div css={{ center: true }}>
            <Span css={{ color: 'text.placeholder', marginRight: 20 }}>{t('router-id')}</Span>
            {isLoading ? <Skeleton block active width={100} height={15} /> : <Span>{data?.router_id ?? '--'}</Span>}
          </Div> */}
        </Div>
      </Div>
      <Toolbar />
      <List />
    </Div>
  );
}

export default BGPneighbors;
