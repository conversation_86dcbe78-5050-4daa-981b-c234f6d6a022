import React from 'react';
import { t } from '@pitrix/portal-widget';
import { portal, Button, Select, RadioButtonGroup, Icon } from '@pitrix/portal-ui';
import { useAtom } from 'jotai';
import { useQueryClient } from 'react-query';

import { ipVersionAtom, refreshIntervalAtom, baseInfoAtom } from '../state';
import { handleAddBGP } from '../modals/index';

const { div: Div } = portal;

const OPTIONS = [
  {
    label: t('关闭', 'Close'),
    value: '0',
  },
  {
    label: t('30秒', '30 sec.'),
    value: '30',
  },
  {
    label: t('1分钟', '1 minute.'),
    value: '60',
  },
  {
    label: t('2分钟', '2 minute.'),
    value: '120',
  },
];

function Toolbar() {
  const [ipVersion, setIpVersion] = useAtom(ipVersionAtom);
  const [time, setTime] = useAtom(refreshIntervalAtom);
  const [baseInfo] = useAtom(baseInfoAtom);

  const queryClient = useQueryClient();
  const handleRefresh = async () => {
    await queryClient.invalidateQueries(['DescribeBorderBgpPeer']);
  };

  return (
    <Div css={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', margin: '16px 0 12px' }}>
      <Button type="primary" onClick={handleAddBGP} disabled={baseInfo?.as_num === undefined}>
        <Icon name="add_fill" style={{ marginRight: 6 }} />
        {t('添加邻居', 'Add Neighbor')}
      </Button>
      <Div css={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        {/* <Input
          clearable
          prefix="MagnifierFill"
          placeholder={t('输入 BGP 邻居 IP 搜索', 'Enter BGP Neighbor IP Search')}
          width={272}
          onClear={() => {
            setSearch('');
          }}
          onPressEnter={(e) => {
            setSearch((e as unknown as React.ChangeEvent<HTMLInputElement>)?.target?.value);
          }}
        /> */}
        <Select
          addonBefore={t('刷新间隔', 'Refresh interval')}
          options={OPTIONS}
          width={80}
          value={time}
          onChange={setTime as () => void}
        />
        <Button icon="refresh_2_fill" onClick={handleRefresh} />
        <Div css={{ height: 16, width: 1, backgroundColor: 'border.shallow' }} />
        <RadioButtonGroup
          value={ipVersion}
          onChange={setIpVersion}
          options={[
            { label: 'IPv4', value: '4' },
            { label: 'IPv6', value: '6' },
          ]}
        />
      </Div>
    </Div>
  );
}

export default Toolbar;
