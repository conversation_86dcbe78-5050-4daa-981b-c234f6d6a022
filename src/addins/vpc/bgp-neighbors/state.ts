import { atom } from 'jotai';
import { Border, BaseInfo } from './types';
// 搜索
export const searchAtom = atom('');

// IP 类型选项
export const ipVersionAtom = atom('4');

// 刷新间隔
export const refreshIntervalAtom = atom('0');

export const limitAtom = atom(10);

export const offsetAtom = atom(0);

export const statusAtom = atom('');

export const paramsAtom = atom((get) => {
  return {
    search_word: get(searchAtom),
    ip_version: get(ipVersionAtom),
    // limit: get(limitAtom),
    // offset: get(offsetAtom),
    status: get(statusAtom),
  };
});

export const borderoAtom = atom<Border>({});

export const baseInfoAtom = atom<BaseInfo>({});

export const borderIdAtom = atom((get) => {
  return get(borderoAtom)?.vpc_border_id || '';
});
