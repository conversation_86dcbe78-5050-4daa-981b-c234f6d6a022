import React, { useEffect } from 'react';
import { useMicAppContext } from 'common/hooks';
import { FormlyModal } from 'components';
import { VPNManagement } from 'pages/vpc/modals';

interface HotPlugExtraParams {
  val1: 'OpenVPN' | 'PPTP' | 'L2TP' | 'ikev2';
  isModify?: boolean;
  router_id: string;
  onCancel: () => void;
  onOk: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  model: any;
}

const VPNManagementModal = (): JSX.Element => {
  const { id, val1, router_id, isModify, onOk, onCancel, model } = useMicAppContext<HotPlugExtraParams>();

  useEffect(() => {
    if (id) {
      FormlyModal.show(id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  return (
    <VPNManagement
      onSuccess={onOk}
      onCancel={onCancel}
      isModify={isModify}
      type={val1}
      router_id={router_id}
      id={id as string}
      model={model}
    />
  );
};

export default VPNManagementModal;
