import { atom } from 'jotai';
import { Loadbalancer } from 'common';
// 搜索
export const searchAtom = atom<string>('');

export const statusAtom = atom('all');

export const zoneAtom = atom<string>('');

export const jobSuccessAtom = atom<boolean>(true);

export const loadbalancerList = atom<Loadbalancer[]>([]);

export const regionMapAtom = atom<Record<string, Loadbalancer[]>>({});

export const limitAtom = atom<number>(10);

export const offsetAtom = atom<number>(0);

export const pagingAtom = atom(
  (get) => {
    const limit = get(limitAtom);
    const offset = get(offsetAtom);
    return {
      limit,
      offset,
      current: (offset + limit) / limit,
    };
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  },
  (_get, set, newValue: any) => {
    const _offset = (newValue.page - 1) * newValue.pageSize;
    set(offsetAtom, _offset);
    set(limitAtom, newValue.pageSize);
  },
);

export const updateZoneAtom = atom(null, (_get, set, zone: string) => {
  set(zoneAtom, zone);
  set(offsetAtom, 0);
});

export const updateStatusAtom = atom(null, (_get, set, status: string) => {
  set(statusAtom, status);
  set(offsetAtom, 0);
});

export const updateSearchAtom = atom(null, (_get, set, word: string) => {
  set(searchAtom, word);
  set(offsetAtom, 0);
});

export const paramsAtom = atom((get) => {
  return {
    extra_id: get(searchAtom) ? get(searchAtom) : undefined,
    lb_status: get(statusAtom) === 'all' ? undefined : get(statusAtom),
    // zone: get(zoneAtom),
    limit: get(limitAtom),
    offset: get(offsetAtom),
  };
});

export const jobIdsAtom = atom<Set<string>>(new Set([]));

// 创建一些修改 Set 的原子
export const addItemAtom = atom(null, (get, set, item: string) => {
  const currentSet = get(jobIdsAtom);
  if (!currentSet.has(item)) {
    const newSet = new Set(currentSet); // 克隆一份新的 Set
    newSet.add(item); // 添加新的元素
    set(jobIdsAtom, newSet); // 更新 atom
  }
});

export const removeItemAtom = atom(null, (get, set, item: string) => {
  const currentSet = get(jobIdsAtom);
  const newSet = new Set(currentSet); // 克隆一份新的 Set
  newSet.delete(item);
  set(jobIdsAtom, newSet); // 更新 atom
});

export const clearSetAtom = atom(null, (get, set) => {
  set(jobIdsAtom, new Set()); // 清空 Set
});
