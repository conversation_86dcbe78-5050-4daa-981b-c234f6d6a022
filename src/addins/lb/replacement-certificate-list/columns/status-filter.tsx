import React from 'react';
import { t } from '@pitrix/portal-widget';
import { FilterCell } from '@pitrix/portal-ui';
import { useAtomValue, useSetAtom } from 'jotai';

import { statusAtom, updateStatusAtom } from '../state';

function StatusFilter(): JSX.Element {
  const status = useAtomValue(statusAtom);
  const setStatus = useSetAtom(updateStatusAtom);

  const options = [
    {
      value: 'all',
      label: t('全部', 'All'),
    },
    { value: '', label: t('已绑定', 'Bound') },
    { value: 'updating', label: t('替换中', 'Replacing') },
    { value: 'failed', label: t('替换失败', 'Replacement Failed') },
    // { value: 'waiting', label: t('等待替换', 'Pending Replacemen') },
  ];

  const handleChange = (v: string) => {
    setStatus(v);
  };

  return (
    <FilterCell value={status} onChange={handleChange as () => void} filters={options}>
      {t('状态', 'Status')}
    </FilterCell>
  );
}

export default StatusFilter;
