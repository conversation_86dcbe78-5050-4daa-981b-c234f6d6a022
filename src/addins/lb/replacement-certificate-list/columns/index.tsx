import React, { useMemo } from 'react';
import { t } from '@pitrix/portal-widget';
import { portal } from '@pitrix/portal-ui';
import { formatTime } from 'utils/dayjs';

import IdName from 'common/components/table/id-name';
import StatusFilter from './status-filter';
import Status from './status';

interface Props {
  data?: unknown;
  jobSuccess?: boolean;
}
export default function useColumns({ jobSuccess }: Props) {
  const columns = useMemo(() => {
    return [
      {
        title: t('负载均衡器 ID', 'Load Balancer ID'),
        key: 'extra_id',
        dataIndex: 'extra_id',
        cellRender: (v: string) => {
          // const lb = data?.loadbalancer_listeners?.find((item) => item?.loadbalancer_id === row?.extra_id);
          // const zone = lb?.zone_id || window?.user.zone;
          return <IdName id={v as string} showCopybtn to={' '} />;
          return v;
        },
      },
      {
        title: <StatusFilter />,
        label: t('状态', 'Status'),
        dataIndex: 'lb_status',
        key: 'lb_status',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        cellRender: (status: string, row: any) => (
          <portal.div css={{ display: 'flex', alignItems: 'center' }}>
            <Status status={jobSuccess ? status || row.transition_status : row.transition_status || status} />
          </portal.div>
        ),
      },
      {
        title: t('更新时间', 'Update Time'),
        label: t('更新时间', 'Update Time'),
        dataIndex: 'status_time',
        key: 'status_time',
        width: 200,
        cellRender: (val: string) => formatTime(val),
      },
    ];
  }, [jobSuccess]);
  return columns;
}
