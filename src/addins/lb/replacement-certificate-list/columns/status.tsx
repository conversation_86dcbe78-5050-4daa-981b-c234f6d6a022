/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { portal } from '@pitrix/portal-ui';
import { keyframes } from '@emotion/react';
import { t } from '@pitrix/portal-widget';

interface Props {
  status: 'success' | 'updating' | 'faild';
}

const keyframe = keyframes`
  0% {
    transform: scale(1.2,1.2);
    opacity: 1;
  }
  20% {
    opacity: 1;
  }
  80% {
    transform: scale(0.8,0.8);
  }
  100% {
    transform: scale(0.8,0.8);
    opacity: 0.2;
  }
`;

const statusMap = {
  success: {
    text: t('已绑定', 'Bound'),
    color: '#15A675',
  },
  updating: {
    text: t('替换中', 'Replacing'),
    color: '#1286F1',
    inExecution: true,
  },
  // waiting: {
  //   text: t('等待替换', 'Pending Replacemen'),
  //   color: '#BB9D1C',
  //   inExecution: true,
  // },
  failed: {
    text: t('替换失败', 'Replacement Failed'),
    color: '#BD3633',
  },
};
function Status({ status }: Props): JSX.Element {
  const color = (statusMap[status as keyof typeof statusMap]?.color || '#BB9D1C') as string;

  const text = statusMap[status as keyof typeof statusMap]?.text || status;

  const inExecution = (statusMap[status] as any)?.inExecution;

  if (!statusMap?.[status]?.text) {
    return <span>--</span>;
  }

  return (
    <portal.div css={{ display: 'flex', alignItems: 'center', gap: 6 }}>
      <portal.div css={{ display: 'flex', alignItems: 'center' }}>
        <portal.div
          css={{
            width: 12,
            height: 12,
            marginRight: 6,
            center: true,
          }}
        >
          <portal.div
            css={{
              center: true,
              width: 12,
              height: 12,
              borderRadius: 'full',
              backgroundColor: `${color}26` as string,
              animation: !!inExecution && `1.2s ease 0s infinite running ${keyframe}`,
            }}
          >
            <portal.div
              css={{
                center: true,
                width: 8,
                height: 8,
                borderRadius: 'full',
                backgroundColor: `${color}76` as string,
              }}
            >
              <portal.div
                css={{
                  width: 6,
                  height: 6,
                  borderRadius: 'full',
                  backgroundColor: color,
                }}
              />
            </portal.div>
          </portal.div>
        </portal.div>
        <portal.div css={{ textBody: 's', color }}>{text}</portal.div>
      </portal.div>
      {/* <Tooltip
        disabled={status !== 'faild'}
        content={t('应用失败，请手动应用修改', 'Application failed please manually apply and modify')}
      >
        <portal.span css={{ color: textMap?.[status]?.color as ColorString }}>{textMap?.[status]?.text}</portal.span>
      </Tooltip> */}
    </portal.div>
  );
}

export default Status;
