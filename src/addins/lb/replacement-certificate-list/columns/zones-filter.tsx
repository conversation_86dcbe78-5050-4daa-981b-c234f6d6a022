import React from 'react';
import { t } from '@pitrix/portal-widget';
import get from 'lodash-es/get';
import { FilterCell } from '@pitrix/portal-ui';
import { useAtomValue, useSetAtom } from 'jotai';

import { zoneAtom, updateZone<PERSON>tom } from '../state';

function ZonesFilter(): JSX.Element {
  const zone = useAtomValue(zoneAtom);
  const setZone = useSetAtom(updateZoneAtom);

  const curZone = {
    value: '',
    label: t('全部', 'All'),
  };

  const userSubZones = window?.user?.zones ?? [];

  const options = userSubZones?.map((i) => ({
    label: get(window.user.zones_info, [i, window.user.lang], i),
    value: i,
  }));

  const handleChange = (v: string) => {
    setZone(v);
  };
  return (
    <FilterCell value={zone} onChange={handleChange as () => void} filters={[curZone, ...options]}>
      {t('区域', 'Zone')}
    </FilterCell>
  );
}

export default ZonesFilter;
