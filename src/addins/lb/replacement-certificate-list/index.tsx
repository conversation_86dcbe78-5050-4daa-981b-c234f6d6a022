import React, { useCallback, useEffect, useMemo } from 'react';
import { t } from '@pitrix/portal-widget';
import { Button, portal, Input, Column } from '@pitrix/portal-ui';
import { useSetAtom, useAtomValue, useAtom } from 'jotai';
import { get } from 'lodash-es';

import { Loadbalancer } from 'common';
import { useMicAppContext, useRequest } from 'common/hooks';
import Table from 'common/components/table';
import useColumns from './columns';
import ReplaceButton from './replace-button';
import {
  updateSearchAtom,
  paramsAtom,
  addItemAtom,
  jobIdsAtom,
  removeItemAtom,
  loadbalancerList,
  pagingAtom,
} from './state';

interface QiankunProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
}
function ReplacementCertificateList(): JSX.Element {
  const { data } = useMicAppContext<QiankunProps>();

  const setSearch = useSetAtom(updateSearchAtom);
  const params = useAtomValue(paramsAtom);
  const setJobIds = useSetAtom(addItemAtom);
  const jobIds = useAtomValue(jobIdsAtom);
  const removeJobIds = useSetAtom(removeItemAtom);
  const setList = useSetAtom(loadbalancerList);
  const [paging, setPagination] = useAtom(pagingAtom);

  const { limit, current } = paging;

  const cacheColumn = useColumns({ data, jobSuccess: !jobIds.size });

  const {
    data: lbList,
    isFetching,
    isLoading,
    refetch,
  } = useRequest(
    {
      action: 'DescribeServerCertLBDependence',
      admin: window.user.user_id,
      server_certificate_id: data.server_certificate_id,
      ...params,
      zone: undefined,
    },
    {
      enabled: !!data.server_certificate_id,
      keepPreviousData: true,
      refetchInterval: jobIds.size ? 5000 : false,
    },
  );

  const dataList = useMemo(() => {
    return (
      lbList?.service_certificate_dependence?.map((item: { lb_status: string; extra_id: string }) => {
        return {
          ...item,
          loadbalancer_id: item?.extra_id,
          lb_status: item.lb_status ? item.lb_status : 'success',
        };
      }) ?? []
    );
  }, [lbList]);

  useEffect(() => {
    setList(dataList);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataList]);

  const watchJobs = useCallback(
    (job) => {
      if (job?.rtype === 'job') {
        const jobInfo = get(job?.resource_set, [0], {});
        const id = get(jobInfo.resources, 'server_cert', '');
        const jobId = get(jobInfo, 'job_id', '');
        const jobStatus = get(jobInfo, 'status', '');
        const jobAction = get(jobInfo, 'job_action', '');
        if (jobStatus && jobIds.has(jobId)) {
          if (jobStatus === 'successful' || jobStatus === 'failed') {
            removeJobIds(jobId);
          }
        }
        if (id && id === data.server_certificate_id) {
          if (!jobIds.size) {
            refetch();
          }
          if (jobId) {
            setJobIds(jobId);
          }
        }
        // 处理接口查询的job推送
        if (
          jobAction === 'ReplaceServerCertsToLBListener' &&
          jobInfo?.directive?.indexOf(data.server_certificate_id) > -1
        ) {
          setJobIds(jobId);
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [jobIds],
  );

  return (
    <div>
      <portal.div css={{ fontSize: '14px', lineHeight: '24px', marginBottom: 12, fontWeight: '500' }}>
        {t('已绑定负载均衡器', 'Bounded Load Balancer')}
      </portal.div>
      <portal.div css={{ display: 'flex', justifyContent: 'space-between', marginBottom: 12 }}>
        <ReplaceButton data={data} refetch={refetch} />
        <portal.div css={{ display: 'flex', alignItems: 'center' }}>
          <Input
            maxLength={100}
            clearable
            width={328}
            prefix="MagnifierFill"
            placeholder={t('输入负载均衡器 ID 搜索', 'Enter Load Balancer ID to search')}
            onClear={() => {
              setSearch('');
            }}
            onPressEnter={(e) => {
              setSearch((e as unknown as React.ChangeEvent<HTMLInputElement>)?.target?.value);
            }}
          />
          {/* todo */}
          <Button icon="refresh2-fill" style={{ marginLeft: '8px', marginRight: '8px' }} onClick={() => refetch()} />
        </portal.div>
      </portal.div>
      <Table
        columns={cacheColumn as Column<Loadbalancer>[]}
        loading={jobIds.size ? isLoading : isFetching}
        rowKey="loadbalancer_id"
        dataSource={dataList || []}
        pagination={{
          showQuickJumper: true,
          showTotal: true,
          showSizeChanger: true,
          pageSize: limit,
          current,
          total: lbList?.total_count ?? 0,
          onPaging: (page: number, pageSize: number) => setPagination({ page, pageSize }),
        }}
        jobActions={['ReplaceServerCertsToLBListener']}
        jobRefresh={(id) => {
          refetch();
          removeJobIds(id as string);
        }}
        jobRtype="loadbalancer"
        watchJobs={watchJobs}
      />
    </div>
  );
}

export default ReplacementCertificateList;
