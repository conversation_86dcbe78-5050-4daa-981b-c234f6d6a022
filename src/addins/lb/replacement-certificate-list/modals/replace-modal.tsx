import React, { useEffect, useMemo } from 'react';
import { t } from '@pitrix/portal-widget';
import { Select, portal, Alert } from '@pitrix/portal-ui';
import { get } from 'lodash-es';
import { useController } from 'react-hook-form';

import { Field } from 'common';
import { useInfiniteRequest } from 'common/hooks';

interface Props {
  data: any;
  cerIDChange: (id: string) => void;
}

function ReplaceModal({ data, cerIDChange }: Props) {
  const curZone = window.user.zone;

  const userSubZones = window?.user?.zones ?? [];

  const renderZones = userSubZones?.length ? userSubZones : [curZone];

  const zoneOptions = renderZones?.map((i) => ({
    label: get(window.user.zones_info, [i, window.user.lang], i),
    value: i,
  }));

  const {
    field: { value: zoneValue, onChange: zoneChange },
  } = useController({
    name: 'zone',
    defaultValue: renderZones[0],
  });

  const {
    field: { value, onChange },
  } = useController({
    name: 'new_certs',
  });

  const {
    data: certificateData,
    hasNextPage,
    error,
    fetchNextPage,
    isFetching,
  } = useInfiniteRequest({
    action: 'DescribeServerCertificates',
    owner: window.user.user_id,
    zone: window.user.zone,
    offset: 0,
    limit: 50,
    visibility: 'all',
  });

  const selectOptions = useMemo(() => {
    return (certificateData?.pages ?? [])
      .flatMap((pages) => pages.server_certificate_set || [])
      .map((item) => ({
        value: item.server_certificate_id,
        label: `${item.server_certificate_id} | ${item.server_certificate_name || '-'}`,
        ...item,
      }))
      .filter((item) => item.server_certificate_id !== data?.server_certificate_id);
  }, [certificateData, data]);

  useEffect(() => {
    cerIDChange(value as string);
  }, [value, cerIDChange]);

  return (
    <div>
      <Alert
        showIcon
        type="info"
        description={t(
          '仅支持一次性批量替换单个可用区下的负载均衡器SSL证书，若需替换多个可用区下的证书，请分批次批量替换。替换过程需要一定时间，请耐心等待。',
          'Only support one-time batch replacement of SSL certificates of load balancer under a single availability zone, if you need to replace the certificates under multiple availability zones, please replace them in batches. If you need to replace certificates under multiple availability zones, please replace them in batches. The replacement process will take some time, so please wait patiently.',
        )}
        closable={false}
        css={{ marginBottom: 20, width: '100%' }}
      />
      <Field label={t('当前证书', 'Current Certificate')}>
        {data.server_certificate_id} | {data?.server_certificate_name ?? '-'}
      </Field>
      <Field label={t('替换证书', 'Replacement Certificate')}>
        <portal.div css={{ width: 328 }}>
          <Select
            placeholder={t('请选择替换证书', 'Please choose to replace the certificate')}
            value={value}
            options={selectOptions}
            loading={isFetching}
            loadingFailed={!!error}
            showBottomText={!hasNextPage}
            onScrollBottom={fetchNextPage}
            onChange={(val) => onChange(val as string)}
          />
          <portal.div css={{ color: 'text.placeholder', marginTop: 4, lineHeight: '20px' }}>
            {t(
              '批量替换的负载均衡器实例将会自动更新为新证书',
              'Batch replaced load balancer instances will be automatically updated with new certificates',
            )}
          </portal.div>
        </portal.div>
      </Field>
      <Field label={t('替换区域', 'Replace Zone')}>
        <portal.div css={{ width: 328 }}>
          <Select
            placeholder={t('请选择所替换证书的区域', 'Please select the zone of the certificate to be replaced')}
            value={zoneValue}
            options={zoneOptions}
            onChange={(val) => zoneChange(val as string)}
          />
          <portal.div css={{ color: 'text.placeholder', marginTop: 4, lineHeight: '20px' }}>
            {t(
              '替换证书的负载均衡器实例所在区域',
              'The zone where the load balancer instance replacing the certificate is located',
            )}
          </portal.div>
        </portal.div>
      </Field>
    </div>
  );
}

export default ReplaceModal;
