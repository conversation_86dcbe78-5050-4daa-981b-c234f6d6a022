import React, { useMemo, useState } from 'react';
import NiceModal from '@ebay/nice-modal-react';
import { Button, Tooltip, Icon } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useAtomValue, useSetAtom } from 'jotai';

import { BaseModal, request } from 'common';
import ReplaceModal from './modals/replace-modal';
import { jobIdsAtom, loadbalancerList, addItemAtom } from './state';

interface Props {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
  refetch: () => void;
}
function ReplaceButton({ data, refetch }: Props): JSX.Element {
  const jobIds = useAtomValue(jobIdsAtom);
  const lbList = useAtomValue(loadbalancerList);
  const setJobIds = useSetAtom(addItemAtom);
  const [cerID, setCerID] = useState('');

  const handleReplace = () => {
    NiceModal.show('replace-cur-certificate');
  };

  const [disabled, disText] = useMemo(() => {
    if (lbList?.length) {
      return [
        !!jobIds.size,
        t('替换将持续一段时间，请耐心等待。', 'The replacement will take some time please be patient.'),
      ];
    }
    return [true, t('暂无绑定的负载均衡器', 'There is currently no bound Load Balancer available')];
  }, [lbList, jobIds]);

  return (
    <div>
      <Tooltip disabled={!disabled} content={disText}>
        <Button type="primary" onClick={handleReplace} disabled={disabled}>
          <Icon name="refresh-fill" style={{ marginRight: '6px' }} />
          {t('替换证书', 'Replace Certificate')}
        </Button>
      </Tooltip>
      <BaseModal
        id="replace-cur-certificate"
        name="replace-cur-certificate"
        width={600}
        title={t('替换证书', 'Replacement Certificate')}
        disabled={!cerID}
        onAsyncOk={async (result) => {
          await request({
            params: {
              action: 'ReplaceServerCertsToLBListener',
              owner: window.user.user_id,
              old_certs: data.server_certificate_id,
              ...result,
            },
          }).then((res) => {
            if (res.job_id) {
              setJobIds(res?.job_id as string);
              refetch?.();
              NiceModal.hide('replace-cur-certificate');
            }
          });
        }}
      >
        <ReplaceModal data={data} cerIDChange={setCerID} />
      </BaseModal>
    </div>
  );
}

export default ReplaceButton;
