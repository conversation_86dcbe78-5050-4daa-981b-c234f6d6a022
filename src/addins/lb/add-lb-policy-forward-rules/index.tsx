import React, { useEffect } from 'react';
import { t } from '@pitrix/portal-widget';
import NiceModal from '@ebay/nice-modal-react';

import { useMicAppContext } from 'common/hooks';
import { BaseModal, request } from 'common';
import ModalForm from './modal-form';

interface CloneVolumeProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
}

export default function AddLbPolicyRules(): JSX.Element {
  const { data, unmountSelf, cb, onCancel } = useMicAppContext<CloneVolumeProps>();

  useEffect(() => {
    if (data) {
      NiceModal.show('add-lb-rules');
    }
  }, [data]);

  return (
    <BaseModal
      id="add-lb-rules"
      name="add-lb-rules"
      width={550}
      title={
        data?.loadbalancer_policy_rule_id
          ? t('修改转发规则', 'Modify Load Balancer Policy Forward Rules')
          : t('添加转发规则', 'Add Load Balancer Policy Forward Rules')
      }
      onCancel={() => {
        unmountSelf?.();
        onCancel?.();
      }}
      onAsyncOk={async (result, close) => {
        const rules = { ...result };
        if (result.rule_type === 'http_header') {
          rules.val = result.header;
          rules.header = undefined;
        }

        let params: any = {
          action: 'AddLoadBalancerPolicyRules',
          loadbalancer_policy: data?.loadbalancer_policy,
          is_top: data?.is_top,
          rules: [rules],
        };

        if (data?.loadbalancer_policy_rule_id) {
          params = {
            action: 'ModifyLoadBalancerPolicyRuleAttributes',
            loadbalancer_policy_rule: data?.loadbalancer_policy_rule_id,
            loadbalancer_policy_rule_name: rules.loadbalancer_policy_rule_name,
            val: rules?.val,
            val2: rules?.val2,
          };
        }

        await request({
          params: {
            ...params,
            owner: window?.user?.user_id,
            zone: data?.zone || window?.user?.zone,
          },
        }).then((res) => {
          if (res?.ret_code === 0) {
            close?.();
            cb?.();
            unmountSelf?.();
          }
        });
      }}
    >
      <ModalForm data={data} />
    </BaseModal>
  );
}
