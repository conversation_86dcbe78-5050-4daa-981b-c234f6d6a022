import React from 'react';
import { Select, Input } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useController } from 'react-hook-form';

import { Field } from 'common';
import HttpField from './http-field';
import ContentField from './content-field';
import ErrorText from './error-text';

const TYPE_OPTIONS = [
  {
    label: t('按域名转发', 'Forward on Domains'),
    value: 'domain',
    tip: t('例如：www.example.com 或者 ^example.com$', 'Example: www.example.com or ^example.com$'),
  },
  {
    label: t('按 URL 转发', 'Forward on URL'),
    value: 'url',
    tip: t('例如：^/static 或者 .css$', 'Example: ^/static or .css$'),
  },
  {
    label: t('按源地址转发', 'Forward by source address'),
    value: 'src',
    tip: t('例如：**************', 'Example: **************'),
  },
  {
    label: t('按 User-Agent 转发', 'Forward by User-Agent'),
    value: 'user_agent',
    tip: '',
  },
  {
    label: t('按 HTTP 请求头转发', 'Forward By HTTP request header'),
    value: 'http_header',
    tip: '',
  },
];

function ModalForm({ data }: any) {
  const {
    field: { value: name, onChange: nameChange },
    formState: { errors },
  } = useController({
    defaultValue: data?.loadbalancer_policy_rule_name || '',
    name: 'loadbalancer_policy_rule_name',
    rules: { required: true },
  });

  const {
    field: { value: type, onChange: typeChange },
  } = useController({
    name: 'rule_type',
    defaultValue: data?.rule_type || 'domain',
  });

  const currentTip = React.useMemo(() => {
    const text = TYPE_OPTIONS.find((item) => item.value === type)?.tip;
    return text ? `${text} \n` : '';
  }, [type]);

  return (
    <div>
      <Field isRequired label={t('名称', 'Name')} css={{ marginBottom: 24 }}>
        <div>
          <Input value={name} onChange={nameChange} error={!!errors.loadbalancer_policy_rule_name} />
          {!!errors.loadbalancer_policy_rule_name && (
            <ErrorText text={t('规则名称不能为空', 'The rule name cannot be empty')} />
          )}
        </div>
      </Field>
      <Field label={t('规则类型', 'Rule Type')} css={{ marginBottom: 24 }}>
        <Select
          width={328}
          placeholder={t('请选择', 'Place select')}
          value={type}
          options={TYPE_OPTIONS}
          onChange={typeChange}
          disabled={!!data?.loadbalancer_policy_rule_id}
        />
      </Field>

      {type === 'http_header' ? (
        <HttpField headerKey={data?.val} content={data?.val2} />
      ) : (
        <ContentField currentTip={currentTip} content={data?.val} />
      )}
    </div>
  );
}

export default ModalForm;
