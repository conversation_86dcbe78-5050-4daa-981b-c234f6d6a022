import React, { useEffect, useState } from 'react';
import { Select, TextArea, portal } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useController } from 'react-hook-form';

import { Field } from 'common';
import ErrorText from './error-text';

const HEADER_OPTIONS = [
  {
    label: 'Host',
    value: 'Host',
  },
  {
    label: 'User-Agent',
    value: 'User-Agent',
  },
  {
    label: 'Referer',
    value: 'Referer',
  },
  {
    label: 'Accept',
    value: 'Accept',
  },
  {
    label: 'Content-Type',
    value: 'Content-Type',
  },
  {
    label: 'Accept-Language',
    value: 'Accept-Language',
  },
];

function HttpField({ headerKey, content }: { headerKey: string; content: string }) {
  const [options, setOptions] = useState(HEADER_OPTIONS);

  const {
    field: { value: header, onChange: headerChange },
    formState: { errors },
  } = useController({
    name: 'header',
    defaultValue: headerKey || 'Host',
    shouldUnregister: true,
    rules: { required: true },
  });

  const {
    field: { value: val2, onChange: val2Change },
  } = useController({
    name: 'val2',
    shouldUnregister: true,
    defaultValue: content || '',
    rules: {
      required: true,
      validate: (v: string) => {
        return /^(?! )[ -~]{1,128}(?<! )$/.test(v);
      },
    },
  });

  useEffect(() => {
    if (headerKey) {
      const isInOptions = options.find((item) => item.value === headerKey);
      if (!isInOptions) {
        setOptions((op) => [...op, { label: headerKey, value: headerKey }]);
      }
    }
  }, [headerKey, options]);

  return (
    <>
      <Field label={t('HTTP 请求头', 'HTTP request header')} css={{ marginBottom: 24 }}>
        <Select
          width={328}
          placeholder={t('请选择', 'Place select')}
          value={header}
          options={options}
          onChange={headerChange}
          addNewOption={{
            rule: /^[a-zA-Z0-9_-]{1,40}$/,
            errText: t(
              '长度限制为 1~40个 字符，只允许包含大小写英文字母、数字、下划线（_）和短划线（-）',
              'The length is limited to 1 to 40 characters, and only case-sensitive letters, numbers, underscores (_) and underscores (-) are allowed.',
            ),
            onSubmit(value) {
              setOptions((op) => {
                return [
                  ...op,
                  {
                    label: value,
                    value,
                  },
                ];
              });
              headerChange(value);
            },
          }}
        />
      </Field>
      <Field label={t('规则内容', 'Rule Content')} isRequired>
        <portal.div>
          <TextArea css={{ width: '328px !important' }} value={val2} onChange={val2Change} error={!!errors.val2} />
          {errors.val2 ? (
            <ErrorText
              text={
                val2
                  ? t(
                      '长度限制为 1~128 个字符，支持可打印字符、大小写英文字母，开头和结尾不能为空格。',
                      'The length is limited to 1~128 characters, support for printable characters, upper and lower case English letters, the beginning and the end can not be a space.',
                    )
                  : t('请求头内容不能为空', 'The request header content cannot be empty')
              }
            />
          ) : (
            <portal.div css={{ color: 'text.placeholder', marginTop: 4, lineHeight: '20px', width: 328 }}>
              {t(
                '长度限制为 1~128 个字符，支持可打印字符、大小写英文字母，开头和结尾不能为空格。',
                'The length is limited to 1~128 characters, support for printable characters, upper and lower case English letters, the beginning and the end can not be a space.',
              )}
            </portal.div>
          )}
        </portal.div>
      </Field>
    </>
  );
}

export default HttpField;
