import React from 'react';
import { TextArea } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';
import { useController } from 'react-hook-form';

import { Field } from 'common';
import ErrorText from './error-text';

function ContentField({ currentTip, content }: { currentTip: string; content: string }) {
  const {
    field: { value: val, onChange: valChange },
    formState: { errors },
  } = useController({
    name: 'val',
    defaultValue: content || '',
    shouldUnregister: true,
    rules: { required: true },
  });

  return (
    <Field label={t('规则内容', 'Rule Content')} isRequired css={{ marginBottom: 24 }}>
      <div>
        <TextArea
          css={{ width: '328px !important' }}
          value={val}
          onChange={valChange}
          error={!!errors.val}
          placeholder={`${currentTip}${t(
            '多个域名或者多个 URL 的正则匹配可以通过空格分割，表示匹配任意一条即可。',
            'The regular match of multiple domains or multiple URLs could be split by a space. It means it will match any one of them.',
          )}`}
        />
        {!!errors.val && <ErrorText text={t('规则内容不能为空', 'The rule content cannot be empty')} />}
      </div>
    </Field>
  );
}

export default ContentField;
