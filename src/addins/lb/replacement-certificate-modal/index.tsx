import { useEffect } from 'react';

import { useMicAppContext } from 'common/hooks';
import { BaseModal } from 'components';
import ReplaceModal from '../replacement-certificate-list/modals/replace-modal';

interface QiankunProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
}
function ReplacementCertificateModal() {
  const { data, unmountSelf } = useMicAppContext<QiankunProps>();

  useEffect(() => {
    if (data) {
      // NiceModal.show('replace-certificate');
      BaseModal.open(ReplaceModal, { data, unmountSelf }, () => {});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return null;
}

export default ReplacementCertificateModal;
