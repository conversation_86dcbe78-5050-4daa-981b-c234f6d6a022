import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// VxNet 资源类型定义
export interface VxnetResource {
  resource_id: string;
  resource_name: string;
  resource_type: string;
  private_ip: string;
  status: string;
  create_time: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

// 请求类型
export interface DescribeVxnetResourcesRequest extends BaseRequest {
  vxnet: string;
  resources?: string[];
  resource_types?: string[];
}

// 响应类型
export interface DescribeVxnetResourcesResponse extends BaseResponse {
  resource_set: VxnetResource[];
}

// 查询键
export const QUERY_KEY_VXNET_RESOURCES = 'DescribeVxnetResources';

/**
 * 查询私有网络资源
 * @param params 请求参数
 * @returns 私有网络资源列表响应
 */
export const describeVxnetResources = (params: DescribeVxnetResourcesRequest) => {
  return request<DescribeVxnetResourcesRequest, DescribeVxnetResourcesResponse>({
    params: {
      action: 'DescribeVxnetResources',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用私有网络资源查询Hook
 */
export const useDescribeVxnetResources = createQueryHook(describeVxnetResources, (params) => [
  QUERY_KEY_VXNET_RESOURCES,
  params,
]);
