import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, ClusterInfo } from '../../types';

// 请求类型
export interface DescribeBasicClusterInfoRequest extends BaseRequest {
  status?: string[];
  clusters?: string[];
  apps?: string[];
  columns?: string[];
  search_word?: string;
  scope?: string[];
  ignore_cfg?: number;
}

// 响应类型
export interface DescribeBasicClusterInfoResponse extends BaseResponse {
  cluster_info: ClusterInfo[];
  cluster_info_count: number;
}

// 查询键
export const QUERY_KEY_BASIC_CLUSTER_INFO = 'DescribeBasicClusterInfo';

/**
 * 查询集群基本信息
 * @param params 请求参数
 * @returns 集群基本信息响应
 */
export const describeBasicClusterInfo = (params: DescribeBasicClusterInfoRequest) => {
  return request<DescribeBasicClusterInfoRequest, DescribeBasicClusterInfoResponse>({
    params: {
      action: 'DescribeBasicClusterInfo',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用集群基本信息查询Hook（带分页信息）
 */
export const useDescribeBasicClusterInfo = createQueryHook(
  describeBasicClusterInfo,
  (params) => [QUERY_KEY_BASIC_CLUSTER_INFO, params],
  (data) => ({
    items: data?.cluster_info || [],
    total: data?.cluster_info_count || 0,
    raw: data,
  }),
);

/**
 * 使用集群基本信息查询Hook（仅返回列表，兼容旧版本）
 */
export const useDescribeBasicClusterInfoList = createQueryHook(
  describeBasicClusterInfo,
  (params) => [QUERY_KEY_BASIC_CLUSTER_INFO, params],
  (data) => data?.cluster_info || [],
);
