import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 用户验证类型定义
export interface UserVerify {
  user_id: string;
  verify_status: string;
  verify_type: string;
  create_time: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

// 请求类型
export interface DescribeUserVerifiesRequest extends BaseRequest {
  users?: string[];
}

// 响应类型
export interface DescribeUserVerifiesResponse extends BaseResponse {
  user_verify_set: UserVerify[];
}

// 查询键
export const QUERY_KEY_USER_VERIFIES = 'DescribeUserVerifies';

/**
 * 查询用户验证信息
 * @param params 请求参数
 * @returns 用户验证信息响应
 */
export const describeUserVerifies = (params: DescribeUserVerifiesRequest) => {
  return request<DescribeUserVerifiesRequest, DescribeUserVerifiesResponse>({
    params: {
      action: 'DescribeUserVerifies',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用用户验证信息查询Hook
 */
export const useDescribeUserVerifies = createQueryHook(describeUserVerifies, (params) => [
  QUERY_KEY_USER_VERIFIES,
  params,
]);
