import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, RouterStatic } from '../../types';

// 请求类型
export interface DescribeRouterStaticsRequest extends BaseRequest {
  router?: string;
  router_static?: string;
  static_type?: number;
  status?: string[];
  search_word?: string;
}

// 响应类型
export interface DescribeRouterStaticsResponse extends BaseResponse {
  total_count: number;
  router_static_set: RouterStatic[];
}

// 查询键
export const QUERY_KEY_ROUTER_STATICS = 'DescribeRouterStatics';

/**
 * 查询路由器静态路由
 * @param params 请求参数
 * @returns 路由器静态路由响应
 */
export const describeRouterStatics = (params: DescribeRouterStaticsRequest) => {
  return request<DescribeRouterStaticsRequest, DescribeRouterStaticsResponse>({
    params: {
      action: 'DescribeRouterStatics',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用路由器静态路由查询Hook
 */
export const useDescribeRouterStatics = createQueryHook(
  describeRouterStatics,
  (params) => [QUERY_KEY_ROUTER_STATICS, params],
  (data) => data,
);

/**
 * 使用路由器静态路由选择器Hook
 * @param params 请求参数
 * @param options 查询选项
 * @returns 格式化后的路由器静态路由和控制函数
 */
export const useDescribeRouterStaticsSelect = (
  params: DescribeRouterStaticsRequest,
  options?: Parameters<typeof useDescribeRouterStatics>[1],
) => {
  return useDescribeRouterStatics(params, {
    ...options,
    select: (data) => {
      return {
        items: data?.router_static_set || [],
        totalCount: data?.total_count || 0,
      };
    },
  });
};
