import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, Eip } from '../../types';

// 请求类型
export interface DescribeEipsRequest extends BaseRequest {
  eips?: string[];
  status?: string[];
  project_id?: string;
}

// 响应类型
export interface DescribeEipsResponse extends BaseResponse {
  eip_set: Eip[];
}

// 查询键
export const QUERY_KEY_EIPS = 'DescribeEips';

/**
 * 查询弹性公网IP
 * @param params 请求参数
 * @returns EIP列表响应
 */
export const describeEips = (params: DescribeEipsRequest) => {
  return request<DescribeEipsRequest, DescribeEipsResponse>({
    params: {
      action: 'DescribeEips',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用EIP查询Hook
 */
export const useDescribeEips = createQueryHook(describeEips, (params) => [QUERY_KEY_EIPS, params]);
