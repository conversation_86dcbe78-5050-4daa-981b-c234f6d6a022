import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, LeaseInfo } from '../../types';

// 请求类型
export interface GetLeaseInfoRequest extends BaseRequest {
  action?: 'GetLeaseInfo';
  resource: string;
}

// 响应类型
export interface GetLeaseInfoResponse extends BaseResponse {
  lease_info: LeaseInfo;
  resource_id: string;
}

// 查询键
export const QUERY_KEY_LEASE_INFO = 'GetLeaseInfo';

/**
 * 查询资源租约信息
 * @param params 请求参数
 * @returns 租约信息响应
 */
export const getLeaseInfo = (params: GetLeaseInfoRequest) => {
  return request<GetLeaseInfoRequest, GetLeaseInfoResponse>({
    params: {
      action: 'GetLeaseInfo',
      zone: window?.user?.zone,
      owner: window?.user?.user_id,
      ...params,
    },
  });
};

/**
 * 使用租约信息查询Hook
 */
export const useGetLeaseInfo = createQueryHook(
  getLeaseInfo,
  (params) => [QUERY_KEY_LEASE_INFO, params],
  (data) => data,
);

/**
 * 使用租约信息选择器Hook
 * @param params 请求参数
 * @param options 查询选项
 * @returns 格式化后的租约信息和控制函数
 */
export const useGetLeaseInfoSelect = (params: GetLeaseInfoRequest, options?: Parameters<typeof useGetLeaseInfo>[1]) => {
  return useGetLeaseInfo(params, {
    ...options,
    select: (data) => {
      const leaseInfo = data?.lease_info;
      if (!leaseInfo) return null;
      return {
        resourceId: data.resource_id,
        status: leaseInfo.status,
        leaseTime: leaseInfo.lease_time,
        renewalTime: leaseInfo.renewal_time,
        chargeMode: leaseInfo.contract?.charge_mode,
        price: leaseInfo.contract?.price,
        startTime: leaseInfo.contract?.start_time,
        endTime: leaseInfo.unlease_time,
        autoRenew: leaseInfo.contract?.auto_renew,
        contractId: leaseInfo.contract?.contract_id,
        priceInfo: leaseInfo.contract?.price_info,
      };
    },
  });
};
