import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, KeyPair } from '../../types';

// 请求类型
export interface DescribeKeyPairsRequest extends BaseRequest {
  keypairs?: string[];
  status?: string[];
  project_id?: string;
}

// 响应类型
export interface DescribeKeyPairsResponse extends BaseResponse {
  keypair_set: KeyPair[];
}

// 查询键
export const QUERY_KEY_KEY_PAIRS = 'DescribeKeyPairs';

/**
 * 查询密钥对
 * @param params 请求参数
 * @returns 密钥对列表响应
 */
export const describeKeyPairs = (params: DescribeKeyPairsRequest) => {
  return request<DescribeKeyPairsRequest, DescribeKeyPairsResponse>({
    params: {
      action: 'DescribeKeyPairs',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用密钥对查询Hook
 */
export const useDescribeKeyPairs = createQueryHook(describeKeyPairs, (params) => [QUERY_KEY_KEY_PAIRS, params]);
