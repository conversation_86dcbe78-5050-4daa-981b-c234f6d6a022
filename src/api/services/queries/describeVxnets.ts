import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, Vxnet } from '../../types';

// 请求类型
export interface DescribeVxnetsRequest extends BaseRequest {
  vxnet_type?: string | string[];
  mode?: number[];
  hypervisor?: string;
  excluded_vxnets?: string[];
}

export interface DescribeVxnetsResponse extends BaseResponse {
  has_share?: boolean;
  vxnet_set: Vxnet[];
}

export const QUERY_KEY_VXNETS = 'DescribeVxnets';

export const describeVxnets = (params: DescribeVxnetsRequest) => {
  return request<DescribeVxnetsRequest, DescribeVxnetsResponse>({
    params: {
      action: QUERY_KEY_VXNETS,
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

export const useDescribeVxnets = createQueryHook(
  describeVxnets,
  (params) => [QUERY_KEY_VXNETS, params],
  (data) => data.vxnet_set,
);
