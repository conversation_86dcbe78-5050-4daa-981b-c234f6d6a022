import { request } from 'common/utils';
import { createQueryHook, createInfiniteQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, PlaceGroup } from '../../types';

// 请求类型
export interface DescribePlaceGroupsRequest extends BaseRequest {
  resource_type?: string;
  pageParam?: number;
  architecture?: string;
  ignore_plg_perm?: number;
}

// 响应类型
export interface DescribePlaceGroupsResponse extends BaseResponse {
  place_group_set: PlaceGroup[];
}

// 查询键
export const QUERY_KEY_PLACE_GROUPS = 'DescribePlaceGroups';

/**
 * 查询资源池
 * @param params 请求参数
 * @returns 资源池列表响应
 */
export const describePlaceGroups = (params: DescribePlaceGroupsRequest) => {
  return request<DescribePlaceGroupsRequest, DescribePlaceGroupsResponse>({
    params: {
      action: 'DescribePlaceGroups',
      status: ['available'],
      resource_type: 'instance',
      offset: 0,
      limit: 50,
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用资源池查询Hook
 */
export const useDescribePlaceGroups = createQueryHook(describePlaceGroups, (params) => [
  QUERY_KEY_PLACE_GROUPS,
  params,
]);

/**
 * 使用无限查询资源池Hook
 */
export const useDescribePlaceGroupsInfinite = createInfiniteQueryHook(
  describePlaceGroups,
  (params) => [QUERY_KEY_PLACE_GROUPS, params],
  (data) => ({
    items: data?.place_group_set || [],
    total: data?.total_count || 0,
  }),
);
