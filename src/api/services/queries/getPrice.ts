import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, PriceSet } from '../../types';

// 请求类型
export interface GetPriceRequest extends BaseRequest {
  currency?: string;
  duration?: number;
  status?: string[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  resources: Record<string, any>[];
}

// 响应类型
export interface GetPriceResponse extends BaseResponse {
  price_set: PriceSet[];
}

// 查询键
export const QUERY_KEY_PRICE = 'GetPrice';

/**
 * 查询价格
 * @param params 请求参数
 * @returns 价格响应
 */
export const getPrice = (params: GetPriceRequest) => {
  return request<GetPriceRequest, GetPriceResponse>({
    params: {
      action: 'GetPrice',
      currency: window.__CURRENCY,
      owner: window?.user?.user_id,
      ...params,
    },
  });
};

/**
 * 使用价格查询Hook
 */
export const useGetPrice = createQueryHook(
  getPrice,
  (params) => [QUERY_KEY_PRICE, params],
  (data) => data?.price_set || [],
);

/**
 * 获取主机价格
 * @param params 主机价格请求参数
 * @returns 价格响应
 */
export const getInstancePrice = (params: {
  zone: string;
  duration?: number;
  resources: {
    cpu: number;
    memory: number;
    instance_type: string;
    instance_class: number;
    image_id: string;
    image_size: number;
    gpu?: number;
    gpu_class?: number;
    instance_class_feature?: number;
    replica_count?: number;
  }[];
}) => {
  const { zone, duration, ...instanceParams } = params;

  return getPrice({
    zone,
    duration,
    ...instanceParams,
  });
};

/**
 * 使用主机价格查询Hook
 */
export const useGetInstancePrice = createQueryHook(
  getInstancePrice,
  (params) => ['GetInstancePrice', params],
  (data) => data?.price_set || [],
);

/**
 * 获取系统盘价格
 */
export const getInstanceOsDiskPrice = (params: {
  zone: string;
  duration?: number;
  cpu: number;
  memory: number;
  instance_type: string;
  instance_class: number;
  image_id: string;
  image_size: number;
  gpu?: number;
  gpu_class?: number;
  instance_class_feature?: number;
  replica_count?: number;
}) => {
  const { zone, duration, ...instanceParams } = params;

  return getPrice({
    zone,
    duration,
    resources: [
      {
        type: 'instance_os_disk',
        sequence: 0,
        ...instanceParams,
      },
    ],
  });
};

/**
 * 使用系统盘价格查询Hook
 */
export const useGetInstanceOsDiskPrice = createQueryHook(
  getInstanceOsDiskPrice,
  (params) => ['GetInstanceOsDiskPrice', params],
  (data) => data?.price_set?.[0] || null,
);

/**
 * 获取硬盘价格
 */
export const getVolumePrice = (params: {
  zone: string;
  duration?: number;
  resources: {
    size: number;
    volume_type: number;
    replica_count?: number;
  }[];
}) => {
  const { zone, duration, resources } = params;

  return getPrice({
    zone,
    duration,
    resources: resources.map((resource, index) => ({
      type: 'volume',
      sequence: index,
      ...resource,
    })),
  });
};

/**
 * 使用硬盘价格查询Hook
 */
export const useGetVolumePrice = createQueryHook(
  getVolumePrice,
  (params) => ['GetVolumePrice', params],
  (data) => data?.price_set || [],
);

/**
 * 获取公网IP价格
 */
export const getEipPrice = (params: {
  zone: string;
  bandwidth: number;
  billing_mode: 'bandwidth' | 'traffic';
  eip_group_id: string;
}) => {
  const { zone, bandwidth, billing_mode, eip_group_id } = params;

  const resources =
    billing_mode === 'bandwidth'
      ? [
          {
            type: 'eip',
            sequence: 0,
            bandwidth,
            billing_mode,
            eip_group_id,
          },
        ]
      : [
          {
            type: 'eip',
            sequence: 1,
            bandwidth,
            billing_mode,
            eip_group_id,
            charge_item: 'eip_traffic',
          },
          {
            type: 'eip',
            sequence: 2,
            bandwidth,
            billing_mode,
            eip_group_id,
            charge_item: 'eip_addr',
          },
        ];

  return getPrice({
    zone,
    resources,
  });
};

/**
 * 使用公网IP价格查询Hook
 */
export const useGetEipPrice = createQueryHook(
  getEipPrice,
  (params) => ['GetEipPrice', params],
  (data) => {
    if (!data?.price_set) return null;

    const result = {
      total: 0,
      bandwidth: null,
      traffic: null,
      address: null,
    };

    data.price_set.forEach((item) => {
      result.total += parseFloat(item.price) || 0;

      if (item.sequence === 0) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        result.bandwidth = item as any;
      } else if (item.sequence === 1) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        result.traffic = item as any;
      } else if (item.sequence === 2) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        result.address = item as any;
      }
    });

    return result;
  },
);

/**
 * 获取IP附加费价格
 */
export const getEipCountPrice = (params: { zone: string; eip_count: number }) => {
  const { zone, eip_count } = params;

  return getPrice({
    zone,
    resources: [
      {
        type: 'eip_count',
        sequence: 0,
        eip_count,
      },
    ],
  });
};

/**
 * 使用IP附加费价格查询Hook
 */
export const useGetEipCountPrice = createQueryHook(
  getEipCountPrice,
  (params) => ['GetEipCountPrice', params],
  (data) => data?.price_set?.[0] || null,
);

/**
 * 获取备份价格
 */
export const getSnapshotPrice = (params: { zone: string }) => {
  const { zone } = params;

  return getPrice({
    zone,
    resources: [
      {
        type: 'snapshot',
        sequence: 0,
        snapshot_type: 1,
        size: 1024,
      },
    ],
  });
};

/**
 * 使用备份价格查询Hook
 */
export const useGetSnapshotPrice = createQueryHook(
  getSnapshotPrice,
  (params) => ['GetSnapshotPrice', params],
  (data) => data?.price_set?.[0] || null,
);

/**
 * 获取负载均衡器价格
 */
export const getLoadBalancerPrice = (params: { zone: string; loadbalancer_type: string; node_count: number }) => {
  const { zone, loadbalancer_type, node_count } = params;

  return getPrice({
    zone,
    resources: [
      {
        type: 'loadbalancer',
        sequence: 0,
        loadbalancer_type,
        node_count,
      },
    ],
  });
};

/**
 * 使用负载均衡器价格查询Hook
 */
export const useGetLoadBalancerPrice = createQueryHook(
  getLoadBalancerPrice,
  (params) => ['GetLoadBalancerPrice', params],
  (data) => data?.price_set?.[0] || null,
);
