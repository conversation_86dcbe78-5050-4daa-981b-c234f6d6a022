import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, InstanceVolumeType } from '../../types';

// 请求类型
export interface DescribeInstanceVolumeTypesRequest extends BaseRequest {
  status?: string[];
  instance_class?: number;
  place_group_id?: string;
}

// 响应类型
export interface DescribeInstanceVolumeTypesResponse extends BaseResponse {
  volume_type_infos: InstanceVolumeType[];
}

// 查询键
export const QUERY_KEY_INSTANCE_VOLUME_TYPES = 'DescribeInstanceVolumeTypes';

/**
 * 查询硬盘类型
 * @param params 请求参数
 * @returns 硬盘类型列表响应
 */
export const describeInstanceVolumeTypes = (params: DescribeInstanceVolumeTypesRequest) => {
  return request<DescribeInstanceVolumeTypesRequest, DescribeInstanceVolumeTypesResponse>({
    params: {
      action: 'DescribeInstanceVolumeTypes',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用硬盘类型查询Hook
 */
export const useDescribeInstanceVolumeTypes = createQueryHook(
  describeInstanceVolumeTypes,
  (params) => [QUERY_KEY_INSTANCE_VOLUME_TYPES, params],
  (data) => data?.volume_type_infos || [],
);
