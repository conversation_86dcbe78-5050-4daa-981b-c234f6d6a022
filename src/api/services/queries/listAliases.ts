import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, Aliase } from '../../types';

export interface ListAliasesRequest extends BaseRequest {
  sort_key: string;
  service: string;
  action?: 'ListAliases';
}

export interface ListAliasesResponse extends BaseResponse {
  action: 'ListAliases';
  total_count: number;
  aliases: Aliase[];
}

export const QUERY_KEY_ALIASES = 'ListAliases';

/**
 * 查询KMS别名列表
 */
export const listAliases = (params: ListAliasesRequest) => {
  return request<ListAliasesRequest, ListAliasesResponse>({
    params: {
      action: 'ListAliases',
      ...params,
    },
  });
};

/**
 * 使用KMS别名列表查询Hook
 */
export const useListAliases = createQueryHook(
  listAliases,
  (params) => [QUERY_KEY_ALIASES, params],
  (data) => data?.aliases || [],
);
