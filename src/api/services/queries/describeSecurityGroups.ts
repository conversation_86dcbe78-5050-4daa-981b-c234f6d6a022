import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, SecurityGroup } from '../../types';

// 请求类型
export interface DescribeSecurityGroupsRequest extends BaseRequest {
  security_groups?: string[];
  status?: string[];
  project_id?: string;
}

// 响应类型
export interface DescribeSecurityGroupsResponse extends BaseResponse {
  security_group_set: SecurityGroup[];
}

// 查询键
export const QUERY_KEY_SECURITY_GROUPS = 'DescribeSecurityGroups';

/**
 * 查询安全组
 * @param params 请求参数
 * @returns 安全组列表响应
 */
export const describeSecurityGroups = (params: DescribeSecurityGroupsRequest) => {
  return request<DescribeSecurityGroupsRequest, DescribeSecurityGroupsResponse>({
    params: {
      action: 'DescribeSecurityGroups',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用安全组查询Hook
 */
export const useDescribeSecurityGroups = createQueryHook(describeSecurityGroups, (params) => [
  QUERY_KEY_SECURITY_GROUPS,
  params,
]);
