import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, Vip } from '../../types';

// 请求类型
export interface DescribeVipsRequest extends BaseRequest {
  vips?: string[];
  vxnets?: string[];
  status?: string[];
  vxnet_type?: number;
}

// 响应类型
export interface DescribeVipsResponse extends BaseResponse {
  vip_set: Vip[];
}

// 查询键
export const QUERY_KEY_VIPS = 'DescribeVips';

/**
 * 查询VIP
 * @param params 请求参数
 * @returns VIP列表响应
 */
export const describeVips = (params: DescribeVipsRequest) => {
  return request<DescribeVipsRequest, DescribeVipsResponse>({
    params: {
      action: 'DescribeVips',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用VIP查询Hook
 */
export const useDescribeVips = createQueryHook(describeVips, (params) => [QUERY_KEY_VIPS, params]);
