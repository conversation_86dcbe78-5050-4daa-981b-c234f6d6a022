import { request } from 'common/utils';
import { createQueryHook, createInfiniteQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, Image } from '../../types';

// 请求类型
export interface DescribeImagesRequest extends BaseRequest {
  provider?: string;
  os_family?: string | string[];
  architecture?: string | string[];
  images?: string[];
  status?: string[];
}

// 响应类型
export interface DescribeImagesResponse extends BaseResponse {
  image_set: Image[];
}

// 查询键
export const QUERY_KEY_IMAGES = 'DescribeImages';

/**
 * 查询镜像
 * @param params 请求参数
 * @returns 镜像列表响应
 */
export const describeImages = (params: DescribeImagesRequest) => {
  return request<DescribeImagesRequest, DescribeImagesResponse>({
    params: {
      action: 'DescribeImages',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用镜像查询Hook
 */
export const useDescribeImages = createQueryHook(
  describeImages,
  (params) => [QUERY_KEY_IMAGES, params],
  (data) => data?.image_set || [],
);

export const useDescribeImagesInfinite = createInfiniteQueryHook(
  describeImages,
  (params) => [QUERY_KEY_IMAGES, params],
  (data) => ({
    items: data?.image_set || [],
    total: data?.total_count || 0,
  }),
);
