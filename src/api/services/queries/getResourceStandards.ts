import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 请求类型
export interface GetResourceStandardsRequest extends BaseRequest {
  plg_id?: string | string[];
  resource_types?:
    | 'all'
    | 'volume_rg'
    | 'instance_rg'
    | 'tps_instance_vg'
    | 'tps_volume_vg'
    | 'tpsc_volume_vg'
    | 'tpsc_instance_vg';
}

type TPSC = {
  conf_id: string;
  vg_name: string;
};

type Options = {
  label: string;
  value: string;
  isVg?: boolean;
};

// 响应类型
export interface GetResourceStandardsResponse extends BaseResponse {
  plg_id?: string;
  volume_rg?: {
    volume: Record<number, string[]>;
  };
  instance_rg?: {
    instance: Record<number, string[]>;
  };
  tps_instance_vg?: Record<number, string[]>;
  tps_volume_vg?: Record<number, string[]>;
  tpsc_instance_vg?: Record<number, TPSC[]>;
  tpsc_volume_vg?: Record<number, TPSC[]>;
}

// 转换后的资源标准类型
export interface ResourceStandardsResult {
  instance: Record<number | string, Options[]>;
  volume: Record<number | string, Options[]>;
}

// 查询键
export const QUERY_KEY_RESOURCE_STANDARDS = 'GetResourceStandards';

/**
 * 查询资源标准
 * @param params 请求参数
 * @returns 资源标准响应
 */
export const getResourceStandards = (params: GetResourceStandardsRequest) => {
  return request<GetResourceStandardsRequest, GetResourceStandardsResponse>({
    params: {
      action: 'GetResourceStandards',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

// 最终返回格式
// const result = {
//   instance: {
//     20: [{ label: 'xxx', value: 'xxx', isVg: true }, { label: 'xxx', value: 'xxx', isVg: true }],
//     100: [{ label: 'xxx', value: 'xxx' }, { label: 'xxx', value: 'xxx' }],
//   },
//   volume: {
//     20: [{ label: 'xxx', value: 'xxx', isVg: true }, { label: 'xxx', value: 'xxx', isVg: true }],
//     100: [{ label: 'xxx', value: 'xxx' }, { label: 'xxx', value: 'xxx' }],
//   },
// };

const transformResourceStandards = (data: GetResourceStandardsResponse) => {
  const result: ResourceStandardsResult = {
    instance: {},
    volume: {},
  };
  Object.entries(data || {}).forEach(([key]) => {
    if (key === 'instance_rg') {
      Object.entries(data?.instance_rg?.instance || {}).forEach(([_class, rgs]) => {
        if (rgs?.length) {
          result.instance[_class] = rgs?.map((v) => ({
            label: v,
            value: v,
          }));
        }
      });
    }

    if (key === 'volume_rg') {
      Object.entries(data?.volume_rg?.volume || {}).forEach(([_type, rgs]) => {
        if (rgs?.length) {
          result.volume[_type] = rgs?.map((v) => ({
            label: v,
            value: v,
          }));
        }
      });
    }

    if (key === 'tps_instance_vg') {
      Object.entries(data?.tps_instance_vg || {}).forEach(([_class, vgs]) => {
        if (vgs?.length) {
          result.instance[_class] = vgs?.map((v) => ({
            label: v,
            value: v,
            isVg: true,
          }));
        }
      });
    }

    if (key === 'tps_volume_vg') {
      Object.entries(data?.tps_volume_vg || {}).forEach(([_type, vgs]) => {
        if (vgs?.length) {
          result.volume[_type] = vgs?.map((v) => ({
            label: v,
            value: v,
            isVg: true,
          }));
        }
      });
    }

    if (key === 'tpsc_instance_vg') {
      Object.entries(data?.tpsc_instance_vg || {}).forEach(([_class, vgs]) => {
        if (vgs?.length) {
          result.instance[_class] = vgs?.map((v) => ({
            label: v.vg_name,
            value: v.conf_id,
            isVg: true,
          }));
        }
      });
    }

    if (key === 'tpsc_volume_vg') {
      Object.entries(data?.tpsc_volume_vg || {}).forEach(([_type, vgs]) => {
        if (vgs?.length) {
          result.volume[_type] = vgs?.map((v) => ({
            label: v.vg_name,
            value: v.conf_id,
            isVg: true,
          }));
        }
      });
    }
  });

  return result;
};

/**
 * 使用资源标准查询Hook
 */
export const useGetResourceStandards = createQueryHook(
  getResourceStandards,
  (params) => [QUERY_KEY_RESOURCE_STANDARDS, params],
  transformResourceStandards,
);
