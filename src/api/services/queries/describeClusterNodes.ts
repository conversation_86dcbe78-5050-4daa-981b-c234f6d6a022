import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, ClusterNode } from '../../types';

// 请求类型
export interface DescribeClusterNodesRequest extends BaseRequest {
  cluster: string;
  nodeZone?: string;
  status?: string[];
  sort_key?: string;
  reverse?: number;
  verbose?: number;
  search_word?: string;
  filter_zone?: string;
}

// 响应类型
export interface DescribeClusterNodesResponse extends BaseResponse {
  node_set: ClusterNode[];
  total_count: number;
  trace_id: string;
}

// 查询键
export const QUERY_KEY_CLUSTER_NODES = 'DescribeClusterNodes';

/**
 * 查询集群节点
 * @param params 请求参数
 * @returns 集群节点响应
 */
export const describeClusterNodes = (params: DescribeClusterNodesRequest) => {
  return request<DescribeClusterNodesRequest, DescribeClusterNodesResponse>({
    params: {
      action: 'DescribeClusterNodes',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用集群节点查询Hook
 */
export const useDescribeClusterNodes = createQueryHook(
  describeClusterNodes,
  (params) => [QUERY_KEY_CLUSTER_NODES, params],
  (data) => ({
    items: data?.node_set || [],
    total: data?.total_count || 0,
    raw: data,
  }),
);
