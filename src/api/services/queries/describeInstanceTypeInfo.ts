import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, InstanceTypeInfo } from '../../types';

// 请求类型
export interface DescribeInstanceTypeInfoRequest extends BaseRequest {
  instance_class?: number;
  features?: number;
}

// 响应类型
export interface DescribeInstanceTypeInfoResponse extends BaseResponse {
  result: InstanceTypeInfo[];
}

// 查询键
export const QUERY_KEY_INSTANCE_TYPE_INFO = 'DescribeInstanceTypeInfo';

/**
 * 查询主机类型配置信息
 * @param params 请求参数
 * @returns 主机类型配置信息响应
 */
export const describeInstanceTypeInfo = (params: DescribeInstanceTypeInfoRequest) => {
  return request<DescribeInstanceTypeInfoRequest, DescribeInstanceTypeInfoResponse>({
    params: {
      action: 'DescribeInstanceTypeInfo',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用主机类型配置信息查询Hook
 */
export const useDescribeInstanceTypeInfo = createQueryHook(
  describeInstanceTypeInfo,
  (params) => [QUERY_KEY_INSTANCE_TYPE_INFO, params],
  (data) => {
    return data?.result?.[0] ?? {};
  },
);
