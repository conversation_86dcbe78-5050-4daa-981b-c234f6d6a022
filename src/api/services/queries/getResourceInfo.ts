import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, ResourceInfo } from '../../types';

// 请求类型
export interface GetResourceInfoRequest extends BaseRequest {
  resources?: string[];
  ignore_system_res?: number;
}

// 响应类型
export interface GetResourceInfoResponse extends BaseResponse {
  resource_info_set: ResourceInfo[];
}

// 查询键
export const QUERY_KEY_RESOURCE_INFO = 'GetResourceInfo';

/**
 * 查询资源信息
 * @param params 请求参数
 * @returns 资源信息响应
 */
export const getResourceInfo = (params: GetResourceInfoRequest) => {
  return request<GetResourceInfoRequest, GetResourceInfoResponse>({
    params: {
      action: 'GetResourceInfo',
      ignore_system_res: 1,
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用资源信息查询Hook
 */
export const useGetResourceInfo = createQueryHook(getResourceInfo, (params) => [QUERY_KEY_RESOURCE_INFO, params]);
