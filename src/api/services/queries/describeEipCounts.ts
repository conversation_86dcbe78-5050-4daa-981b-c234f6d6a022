import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// EIP 计数类型定义
export interface EipCount {
  eip_count: number;
  zone_id: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

// 请求类型
export interface DescribeEipCountsRequest extends BaseRequest {
  project_id?: string;
}

// 响应类型
export interface DescribeEipCountsResponse extends BaseResponse {
  eip_count_set: EipCount[];
}

// 查询键
export const QUERY_KEY_EIP_COUNTS = 'DescribeEipCounts';

/**
 * 查询EIP计数
 * @param params 请求参数
 * @returns EIP计数响应
 */
export const describeEipCounts = (params: DescribeEipCountsRequest) => {
  return request<DescribeEipCountsRequest, DescribeEipCountsResponse>({
    params: {
      action: 'DescribeEipCounts',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用EIP计数查询Hook
 */
export const useDescribeEipCounts = createQueryHook(describeEipCounts, (params) => [QUERY_KEY_EIP_COUNTS, params]);
