import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, InstanceStyle, InstanceStyleOption } from '../../types';

// 请求类型
export interface DescribeInstanceStylesRequest extends BaseRequest {
  place_group_id?: string | string[];
}

// 响应类型
export interface DescribeInstanceStylesResponse extends BaseResponse {
  instance_style: InstanceStyle[];
}

// 查询键
export const QUERY_KEY_INSTANCE_STYLES = 'DescribeInstanceStyles';

/**
 * 查询主机风格
 * @param params 请求参数
 * @returns 主机风格列表响应
 */
export const describeInstanceStyles = (params: DescribeInstanceStylesRequest) => {
  return request<DescribeInstanceStylesRequest, DescribeInstanceStylesResponse>({
    params: {
      action: 'DescribeInstanceStyles',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 根据产品中心获取style名称
 * @param key 风格键名
 * @param defaultValue 默认值
 * @returns 风格名称
 */
export const getInstanceStyleName = (key: string, defaultValue: string) => {
  return window?.p_text?.(`instance.attrs.instance_style.${key}`) || defaultValue;
};

/**
 * 转换实例风格数据为选项格式
 * @param data 原始响应数据
 * @returns 转换后的实例风格选项数组
 */
const transformInstanceStyles = (data: DescribeInstanceStylesResponse): InstanceStyleOption[] => {
  if (!data?.instance_style || !Array.isArray(data.instance_style)) {
    return [];
  }

  return data.instance_style
    .flatMap((styleObj) => {
      // 每个styleObj是一个对象，其中键是风格名称，值是资源类别数组
      return Object.entries(styleObj).map(([style, resourceClass]) => ({
        label: getInstanceStyleName(style, style),
        value: style,
        resourceClass,
        description: getInstanceStyleName(`${style}.description`, ''),
        name: getInstanceStyleName(style, style),
        tooltip: {
          content: getInstanceStyleName(`${style}.description`, ''),
        },
      }));
    })
    .filter((item) => item.resourceClass && item.resourceClass.length > 0); // 只返回有资源类别的风格
};

/**
 * 使用主机风格查询Hook
 * 返回转换后的数据格式
 */
export const useDescribeInstanceStyles = createQueryHook(
  describeInstanceStyles,
  (params) => [QUERY_KEY_INSTANCE_STYLES, params],
  transformInstanceStyles, // 传入数据转换函数
);

/**
 * 使用主机风格查询Hook
 * 返回原始数据
 */
export const useDescribeInstanceStylesRaw = createQueryHook(describeInstanceStyles, (params) => [
  QUERY_KEY_INSTANCE_STYLES,
  params,
]);
