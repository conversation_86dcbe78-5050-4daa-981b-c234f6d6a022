import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, OsFamily } from '../../types';

// 请求类型
export interface GetImageOsFamilyRequest extends BaseRequest {
  visibility?: string[];
  architecture?: string | string[];
}

// 响应类型
export interface GetImageOsFamilyResponse extends BaseResponse {
  os_family: OsFamily;
}

// 查询键
export const QUERY_KEY_IMAGE_OS_FAMILY = 'GetImageOsFamily';

/**
 * 查询镜像家族
 * @param params 请求参数
 * @returns 镜像家族响应
 */
export const getImageOsFamily = (params: GetImageOsFamilyRequest) => {
  return request<GetImageOsFamilyRequest, GetImageOsFamilyResponse>({
    params: {
      action: 'GetImageOsFamily',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用镜像家族查询Hook
 */
export const useGetImageOsFamily = createQueryHook(getImageOsFamily, (params) => [QUERY_KEY_IMAGE_OS_FAMILY, params]);
