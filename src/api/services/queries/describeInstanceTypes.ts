import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, InstanceType } from '../../types';

// 请求类型
export interface DescribeInstanceTypesRequest extends BaseRequest {
  instance_style?: string;
  resource_class?: number | string[] | number[];
  place_group_id?: string | string[];
  resource_type?: 'instance';
  gpu_current?: string;
}

// 响应类型
export interface DescribeInstanceTypesResponse extends BaseResponse {
  instance_type_set: InstanceType[];
}

// 查询键
export const QUERY_KEY_INSTANCE_TYPES = 'DescribeInstanceTypes';

/**
 * 查询主机类型
 * @param params 请求参数
 * @returns 主机类型列表响应
 */
export const describeInstanceTypes = (params: DescribeInstanceTypesRequest) => {
  return request<DescribeInstanceTypesRequest, DescribeInstanceTypesResponse>({
    params: {
      action: 'DescribeInstanceTypes',
      resource_type: 'instance',
      status: ['available'],
      offset: 0,
      limit: 999,
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  }).then((response) => {
    // 直接过滤gpu筛选，目前接口不支持该字段
    if (params.gpu_current !== undefined) {
      return {
        ...response,
        instance_type_set: response.instance_type_set?.filter((item) => item.gpu_count === Number(params.gpu_current)),
      };
    }
    return response;
  });
};

/**
 * 使用主机类型查询Hook
 */
export const useDescribeInstanceTypes = createQueryHook(
  describeInstanceTypes,
  (params) => [QUERY_KEY_INSTANCE_TYPES, params],
  (data) => data?.instance_type_set || [],
);
