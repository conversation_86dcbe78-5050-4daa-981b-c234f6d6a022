import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

interface Result {
  code_value: string;
  values: string[];
}

interface Params extends BaseRequest {
  prod_id?: string;
  spec_id?: string;
  code?: string;
  target_code: string;
}

export interface DescribefilterStatisticsRequest extends BaseRequest {
  action?: 'ProductCenterQueryRequest';
  method?: 'get' | 'post';
  path?: string;
  params?: string;
}

export interface DescribefilterStatisticsResponse extends BaseResponse {
  target_values: Result[];
}

export const QUERY_KEY_PRODUCT_CENTER = 'ProductCenterQueryRequest';

export const describeProductCenter = (params: Params) =>
  request<DescribefilterStatisticsRequest, DescribefilterStatisticsResponse>({
    params: {
      action: 'ProductCenterQueryRequest',
      path: '/v1/skus:filterStatistics',
      method: 'get',
      params: JSON.stringify({
        prod_id: 'instance',
        spec_id: 'instance',
        code: 'zone_id',
        ...params,
      }),
    },
  });

export const useDescribeProductCenter = createQueryHook(describeProductCenter, (params) => [
  QUERY_KEY_PRODUCT_CENTER,
  params,
]);
