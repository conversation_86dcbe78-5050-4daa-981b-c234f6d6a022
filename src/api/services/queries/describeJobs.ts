import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, Job } from '../../types';

// 请求类型
export interface DescribeJobsRequest extends BaseRequest {
  resource_ids?: string;
}

// 响应类型
export interface DescribeJobsResponse extends BaseResponse {
  job_set: Job[];
}

// 查询键
export const QUERY_KEY_JOBS = 'DescribeJobs';

/**
 * 查询任务
 * @param params 请求参数
 * @returns 任务列表响应
 */
export const describeJobs = (params: DescribeJobsRequest) => {
  return request<DescribeJobsRequest, DescribeJobsResponse>({
    params: {
      action: 'DescribeJobs',
      status: [],
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用任务查询Hook
 */
export const useDescribeJobs = createQueryHook(describeJobs, (params) => [QUERY_KEY_JOBS, params]);
