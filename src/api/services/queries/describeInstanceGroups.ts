import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, InstanceGroup } from '../../types';

// 请求类型
export interface DescribeInstanceGroupsRequest extends BaseRequest {
  instance_group_id?: string;
}

// 响应类型
export interface DescribeInstanceGroupsResponse extends BaseResponse {
  instance_group_set: InstanceGroup[];
}

// 查询键
export const QUERY_KEY_INSTANCE_GROUPS = 'DescribeInstanceGroups';

/**
 * 查询实例组
 * @param params 请求参数
 * @returns 实例组列表响应
 */
export const describeInstanceGroups = (params: DescribeInstanceGroupsRequest) => {
  return request<DescribeInstanceGroupsRequest, DescribeInstanceGroupsResponse>({
    params: {
      action: 'DescribeInstanceGroups',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用实例组查询Hook
 */
export const useDescribeInstanceGroups = createQueryHook(
  describeInstanceGroups,
  (params) => [QUERY_KEY_INSTANCE_GROUPS, params],
  (data) => data?.instance_group_set || [],
);
