import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, ChargeRecord } from '../../types';

// 请求类型
export interface GetChargeRecordsRequest extends BaseRequest {
  action?: 'GetChargeRecords';
  resource: string;
  no_zone?: boolean;
}

// 响应类型
export interface GetChargeRecordsResponse extends BaseResponse {
  charge_record_set: ChargeRecord[];
  total_sum?: string;
}
// 查询键
export const QUERY_KEY_CHARGE_RECORDS = 'GetChargeRecords';

/**
 * 查询资源计费记录
 * @param params 请求参数
 * @returns 计费记录响应
 */
export const getChargeRecords = (params: GetChargeRecordsRequest) => {
  return request<GetChargeRecordsRequest, GetChargeRecordsResponse>({
    params: {
      action: 'GetChargeRecords',
      no_zone: false,
      zone: window?.user?.zone,
      owner: window?.user?.user_id,
      ...params,
    },
  });
};

/**
 * 使用计费记录查询Hook
 */
export const useGetChargeRecords = createQueryHook(
  getChargeRecords,
  (params) => [QUERY_KEY_CHARGE_RECORDS, params],
  (data) => data,
);

/**
 * 使用计费记录选择器Hook
 * @param params 请求参数
 * @param options 查询选项
 * @returns 格式化后的计费记录和控制函数
 */
export const useGetChargeRecordsSelect = (
  params: GetChargeRecordsRequest,
  options?: Parameters<typeof useGetChargeRecords>[1],
) => {
  return useGetChargeRecords(params, {
    ...options,
    select: (data) => {
      return {
        records: data?.charge_record_set || [],
        totalSum: data?.total_sum || '0',
        totalCount: data?.total_count || 0,
      };
    },
  });
};
