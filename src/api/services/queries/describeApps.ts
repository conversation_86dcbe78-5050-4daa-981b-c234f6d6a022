import { request } from 'common/utils';
import { createQueryHook, createInfiniteQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, App } from '../../types';

// 请求类型
export interface DescribeAppsRequest extends BaseRequest {
  app_type: string;
  category?: string;
}

// 响应类型
export interface DescribeAppsResponse extends BaseResponse {
  app_set: App[];
}

// 查询键
export const QUERY_KEY_APPS = 'DescribeApps';

/**
 * 查询镜像市场
 */
export const describeApps = (params: DescribeAppsRequest) => {
  return request<DescribeAppsRequest, DescribeAppsResponse>({
    params: {
      action: 'DescribeApps',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用镜像市场查询Hook
 */
export const useDescribeApps = createQueryHook(describeApps, (params) => [QUERY_KEY_APPS, params]);

export const useDescribeAppsInfinite = createInfiniteQueryHook(
  describeApps,
  (params) => [QUERY_KEY_APPS, params],
  (data) => ({
    items: data?.app_set || [],
    total: data?.total_count || 0,
  }),
);
