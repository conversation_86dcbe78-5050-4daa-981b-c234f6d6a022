import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 网络ACL类型定义
export interface NetworkACL {
  network_acl_id: string;
  network_acl_name: string;
  description?: string;
  status: string;
  create_time: string;
  status_time: string;
  is_default: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

// 请求类型
export interface DescribeNetworkACLsRequest extends BaseRequest {
  network_acls?: string[];
  status?: string[];
  project_id?: string;
}

// 响应类型
export interface DescribeNetworkACLsResponse extends BaseResponse {
  network_acl_set: NetworkACL[];
}

// 查询键
export const QUERY_KEY_NETWORK_ACLS = 'DescribeNetworkACLs';

/**
 * 查询网络ACL
 * @param params 请求参数
 * @returns 网络ACL列表响应
 */
export const describeNetworkACLs = (params: DescribeNetworkACLsRequest) => {
  return request<DescribeNetworkACLsRequest, DescribeNetworkACLsResponse>({
    params: {
      action: 'DescribeNetworkACLs',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用网络ACL查询Hook
 */
export const useDescribeNetworkACLs = createQueryHook(describeNetworkACLs, (params) => [
  QUERY_KEY_NETWORK_ACLS,
  params,
]);
