import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 请求类型
export interface DescribeVpcUsedIpRequest extends BaseRequest {
  vpc?: string;
  vxnet?: string;
  ip?: string;
  status?: string[];
  search_word?: string;
}

// 响应类型
export interface DescribeVpcUsedIpResponse extends BaseResponse {
  total_count: number;
  ip_set: {
    ip: string;
    vpc_id: string;
    vxnet_id: string;
    status: string;
    resource_id: string;
    resource_type: string;
    resource_name: string;
    create_time: string;
  }[];
}

// 查询键
export const QUERY_KEY_VPC_USED_IP = 'DescribeVpcUsedIp';

/**
 * 查询VPC已使用IP
 */
export const describeVpcUsedIp = (params: DescribeVpcUsedIpRequest) => {
  return request<DescribeVpcUsedIpRequest, DescribeVpcUsedIpResponse>({
    params: {
      action: 'DescribeVpcUsedIp',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用VPC已使用IP查询Hook
 */
export const useDescribeVpcUsedIp = createQueryHook(describeVpcUsedIp, (params) => [QUERY_KEY_VPC_USED_IP, params]);
