import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, Instance } from '../../types';

// 请求类型
export interface DescribeInstancesRequest extends BaseRequest {
  instances?: string[];
  status?: string[];
  instance_type?: string[];
  tags?: string[];
  search_word?: string;
  mount_detail?: number;
  directory_id?: string;
}

// 响应类型
export interface DescribeInstancesResponse extends BaseResponse {
  instance_set: Instance[];
}

// 查询键
export const QUERY_KEY_INSTANCES = 'DescribeInstances';

/**
 * 查询实例列表
 * @param params 请求参数
 * @returns 实例列表响应
 */
export const describeInstances = (params: DescribeInstancesRequest) => {
  return request<DescribeInstancesRequest, DescribeInstancesResponse>({
    params: {
      action: 'DescribeInstances',
      verbose: 1,
      offset: 0,
      limit: 20,
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用实例列表查询Hook
 */
export const useDescribeInstances = createQueryHook(describeInstances, (params) => [QUERY_KEY_INSTANCES, params]);
