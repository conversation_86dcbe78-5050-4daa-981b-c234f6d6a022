import { request } from 'common/utils';
import { createQueryHook, createInfiniteQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, Volume } from '../../types';

// 请求类型
export interface DescribeVolumesRequest extends BaseRequest {
  offset?: number;
  owner?: string;
  status?: string[];
  search_word?: string;
  volume_type?: number[];
}

// 响应类型
export interface DescribeVolumesResponse extends BaseResponse {
  volume_set: Volume[];
}

// 查询键
export const QUERY_KEY_VOLUMES = 'DescribeVolumes';

/**
 * 查询硬盘列表
 */
export const describeVolumes = (params: DescribeVolumesRequest) => {
  return request<DescribeVolumesRequest, DescribeVolumesResponse>({
    params: {
      action: 'DescribeVolumes',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用硬盘列表查询Hook
 */
export const useDescribeVolumes = createQueryHook(
  describeVolumes,
  (params) => [QUERY_KEY_VOLUMES, params],
  (data) => data.volume_set,
);

export const useDescribeVolumesInfinite = createInfiniteQueryHook(
  describeVolumes,
  (params) => [QUERY_KEY_VOLUMES, params],
  (data) => ({
    items: data?.volume_set || [],
    total: data?.total_count || 0,
  }),
);
