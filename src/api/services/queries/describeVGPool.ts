import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, VgPool, ResourceStandardsOption } from '../../types';

// 请求存储池接口参数
export interface DescribeVGGroupRequest extends BaseRequest {
  action?: 'DescribeVGPool';
}

export interface DescribeVGGroupResponse extends BaseResponse {
  action: 'DescribeVGPoolResponse';
  vg_pool_set?: VgPool[];
}

export interface VGPoolOptions extends VgPool {
  label: string;
  value: string;
  is_default: number;
  vgGroups: ResourceStandardsOption[];
}

// 查询键
export const QUERY_KEY_VG_POOL = 'DescribeVGPool';

/**
 * 获取存储组
 * @param params 请求参数
 * @returns 存储组响应
 */
export const describeVGPool = (params: DescribeVGGroupRequest) => {
  return request<DescribeVGGroupRequest, DescribeVGGroupResponse>({
    params: {
      action: 'DescribeVGPool',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用存储组查询Hook
 */
export const useGetDescribeVGPool = createQueryHook(
  describeVGPool,
  (params) => [QUERY_KEY_VG_POOL, params],
  (data) => {
    const result = data?.vg_pool_set || [];
    // 把is_default为1的排在最前面
    result.sort((a, b) => b.is_default - a.is_default);
    return result?.map((item) => {
      // 处理存储组下的存储池，并添加isVg属性
      const vgGroups = item.container_confs?.map((conf) => ({
        label: conf.value,
        value: conf.container_conf_id,
        isVg: true,
      }));

      return {
        ...item,
        label: item.container_conf_pool_name,
        value: item.container_conf_pool_id,
        vgGroups,
      };
    });
  },
);
