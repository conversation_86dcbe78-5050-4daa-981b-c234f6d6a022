import { request } from 'common/utils';
import { createQueryHook } from '../../hooks';

import type { BaseRequest, BaseResponse, ImageUsers } from '../../types';

// 请求类型
export interface DescribeImageUsersRequest extends BaseRequest {
  image_id?: string;
}

// 响应类型
export interface DescribeImageUsersResponse extends BaseResponse {
  image_user_set: ImageUsers[];
}

// 查询键
export const QUERY_KEY_IMAGE_USERS = 'DescribeImageUsers';

/**
 * 查询镜像用户
 * @param params 请求参数
 * @returns 镜像用户列表响应
 */
export const describeImageUsers = (params: DescribeImageUsersRequest) => {
  return request<DescribeImageUsersRequest, DescribeImageUsersResponse>({
    params: {
      action: 'DescribeImageUsers',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用镜像用户查询Hook
 */
export const useDescribeImageUsers = createQueryHook(describeImageUsers, (params) => [QUERY_KEY_IMAGE_USERS, params]);
