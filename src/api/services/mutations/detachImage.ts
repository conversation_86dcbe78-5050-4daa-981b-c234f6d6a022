import { request } from 'common/utils';
import { createMutationHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 请求类型
export interface DetachImageRequest extends BaseRequest {
  instance?: string;
  image?: string;
}

/**
 * 卸载镜像
 * @param params 请求参数
 * @returns 卸载镜像响应
 */
export const detachImage = (params: DetachImageRequest) => {
  return request<DetachImageRequest, BaseResponse>({
    params: {
      action: 'DetachImage',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用卸载镜像Hook
 * 可以在使用时通过 invalidateQueries 指定需要刷新的查询
 */
export const useDetachImage = createMutationHook<DetachImageRequest, BaseResponse>(detachImage);
