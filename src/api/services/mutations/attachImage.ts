import { request } from 'common/utils';
import { createMutationHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 请求类型
export interface AttachImageRequest extends BaseRequest {
  instance?: string;
  image?: string;
}

/**
 * 挂载镜像
 * @param params 请求参数
 * @returns 挂载镜像响应
 */
export const attachImage = (params: AttachImageRequest) => {
  return request<AttachImageRequest, BaseResponse>({
    params: {
      action: 'AttachImage',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用挂载镜像Hook
 * 可以在使用时通过 invalidateQueries 指定需要刷新的查询
 */
export const useAttachImage = createMutationHook<AttachImageRequest, BaseResponse>(attachImage);
