import { request } from 'common/utils';
import { createMutationHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 请求类型
export interface LeaveProjectRequest extends BaseRequest {
  resources: string[];
  project_id: string[];
}

/**
 * 离开项目
 * @param params 请求参数
 * @returns
 */
export const leaveProject = (params: LeaveProjectRequest) => {
  return request<LeaveProjectRequest, BaseResponse>({
    params: {
      action: 'DeleteProjectResourceItems',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 离开项目Hook
 */
export const useLeavePeoject = createMutationHook<LeaveProjectRequest, BaseResponse>(leaveProject);
