import { request } from 'common/utils';
import { createMutationHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 请求类型
export interface AllocateVipsRequest extends BaseRequest {
  vxnet?: string;
  count?: number;
  vip_name?: string;
  vip_addrs?: string[];
}

// 响应类型
export interface AllocateVipsResponse extends BaseResponse {
  vips: string[];
  job_id: string;
}

/**
 * 分配VIP
 * @param params 请求参数
 * @returns 分配VIP响应
 */
export const allocateVips = (params: AllocateVipsRequest) => {
  return request<AllocateVipsRequest, AllocateVipsResponse>({
    params: {
      action: 'AllocateVips',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用分配VIP Hook
 * 可以在使用时通过 invalidateQueries 指定需要刷新的查询
 */
export const useAllocateVips = createMutationHook<AllocateVipsRequest, AllocateVipsResponse>(allocateVips);
