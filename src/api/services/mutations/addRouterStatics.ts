import { request } from 'common/utils';
import { createMutationHook } from '../../hooks';

import type { BaseRequest, BaseResponse, RouterStatic } from '../../types';

// 请求类型
export interface AddRouterStaticsRequest extends BaseRequest {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  statics: Record<string, any>[];
  router: string;
}

// 响应类型
export interface AddRouterStaticsResponse extends BaseResponse {
  total_count: number;
  router_static_set: RouterStatic[];
}

/**
 * 添加路由器静态路由
 * @param params 请求参数
 * @returns 添加路由器静态路由响应
 */
export const addRouterStatics = (params: AddRouterStaticsRequest) => {
  return request<AddRouterStaticsRequest, AddRouterStaticsResponse>({
    params: {
      action: 'AddRouterStatics',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用添加路由器静态路由Hook
 * 可以在使用时通过 invalidateQueries 指定需要刷新的查询
 */
export const useAddRouterStatics = createMutationHook<AddRouterStaticsRequest, AddRouterStaticsResponse>(
  addRouterStatics,
);
