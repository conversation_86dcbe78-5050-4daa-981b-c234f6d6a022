import { request } from 'common/utils';
import { createMutationHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 请求类型
export interface AddSpanMembersRequest extends BaseRequest {
  span: string;
  selectedData: string[];
  resources: string[];
}

// 响应类型
export interface AddSpanMembersResponse extends BaseResponse {
  action: 'AddSpanMembersResponse';
  job_id: string;
  ret_code: 0;
}

/**
 * 添加Node到span中
 * @param params 请求参数
 * @returns  添加Node到span响应
 */
export const addSpanMembers = (params: AddSpanMembersRequest) => {
  return request<AddSpanMembersRequest, AddSpanMembersResponse>({
    params: {
      action: 'AddSpanMembers',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 添加Node到spanHook
 * 可以在使用时通过 invalidateQueries 指定需要刷新的查询
 */
export const useAddSpanMembers = createMutationHook<AddSpanMembersRequest, AddSpanMembersResponse>(addSpanMembers);
