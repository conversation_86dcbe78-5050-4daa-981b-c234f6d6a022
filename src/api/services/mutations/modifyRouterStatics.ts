import { request } from 'common/utils';
import { createMutationHook } from '../../hooks';

import type { BaseRequest, BaseResponse, RouterStatic } from '../../types';

// 请求类型
export interface ModifyRouterStaticsRequest extends BaseRequest {
  router_static: string;
  val2?: string;
  val3?: string;
  val4?: string;
  val5?: string;
  val6?: string;
  val7?: string;
  val8?: string;
}

// 响应类型
export interface ModifyRouterStaticsResponse extends BaseResponse {
  total_count: number;
  router_static_set: RouterStatic[];
}

/**
 * 修改路由器静态路由属性
 * @param params 请求参数
 * @returns 修改路由器静态路由属性响应
 */
export const modifyRouterStatics = (params: ModifyRouterStaticsRequest) => {
  return request<ModifyRouterStaticsRequest, ModifyRouterStaticsResponse>({
    params: {
      action: 'ModifyRouterStaticAttributes',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用修改路由器静态路由属性Hook
 * 可以在使用时通过 invalidateQueries 指定需要刷新的查询
 */
export const useModifyRouterStatics = createMutationHook<ModifyRouterStaticsRequest, ModifyRouterStaticsResponse>(
  modifyRouterStatics,
);
