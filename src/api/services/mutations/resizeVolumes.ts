import { request } from 'common/utils';
import { createMutationHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 请求类型
export interface ResizeVolumesRequest extends BaseRequest {
  volume_ids: string;
  volume_sizes: string;
}

// 响应类型
export interface ResizeVolumesResponse extends BaseResponse {
  job_id: string;
}

/**
 * 调整硬盘大小
 * @param params 请求参数
 * @returns 调整硬盘大小响应
 */
export const resizeVolumes = (params: ResizeVolumesRequest) => {
  return request<ResizeVolumesRequest, ResizeVolumesResponse>({
    params: {
      action: 'ResizeVolumes',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用调整硬盘大小Hook
 * 可以在使用时通过 invalidateQueries 指定需要刷新的查询
 */
export const useResizeVolumes = createMutationHook<ResizeVolumesRequest, ResizeVolumesResponse>(resizeVolumes);
