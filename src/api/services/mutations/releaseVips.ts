import { request } from 'common/utils';
import { createMutationHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 请求类型
export interface ReleaseVipsRequest extends BaseRequest {
  vips: string[];
}

/**
 * 释放VIP
 * @param params 请求参数
 * @returns 释放VIP响应
 */
export const releaseVips = (params: ReleaseVipsRequest) => {
  return request<ReleaseVipsRequest, BaseResponse>({
    params: {
      action: 'ReleaseVips',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用释放VIP Hook
 * 可以在使用时通过 invalidateQueries 指定需要刷新的查询
 */
export const useReleaseVips = createMutationHook<ReleaseVipsRequest, BaseResponse>(releaseVips);
