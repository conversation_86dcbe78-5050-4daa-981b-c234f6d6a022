import { request } from 'common/utils';
import { createMutationHook } from '../../hooks';

import type { BaseRequest, BaseResponse } from '../../types';

// 请求类型
export interface ModifyVipAttributesRequest extends BaseRequest {
  vip: string;
  vip_name?: string;
  description?: string;
}

/**
 * 修改VIP属性
 * @param params 请求参数
 * @returns 修改VIP属性响应
 */
export const modifyVipAttributes = (params: ModifyVipAttributesRequest) => {
  return request<ModifyVipAttributesRequest, BaseResponse>({
    params: {
      action: 'ModifyVipAttributes',
      owner: window?.user?.user_id,
      zone: window?.user?.zone,
      ...params,
    },
  });
};

/**
 * 使用修改VIP属性Hook
 * 可以在使用时通过 invalidateQueries 指定需要刷新的查询
 */
export const useModifyVipAttributes = createMutationHook<ModifyVipAttributesRequest, BaseResponse>(modifyVipAttributes);
