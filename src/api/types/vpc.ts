export interface RouterStatic {
  console_id: string;
  controller: string;
  create_time: string;
  description: string;
  disabled: number;
  entry_set: string[];
  owner: string;
  root_user_id: string;
  router_id: string;
  router_static_id: string;
  router_static_name: string;
  static_type: number;
  val1: string;
  val2: string;
  val3: string;
  val4: string;
  val5: string;
  val6: string;
  val7: string;
  val8: string;
  vxnet_id: string;
}
