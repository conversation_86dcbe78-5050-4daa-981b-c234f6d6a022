import { Eip } from './eip';
import { Image } from './image';
import { BaseVolume } from './volume';

type ResourceProjectInfo = {
  project_id?: string;
  project_name?: string;
  meta?: string;
};

export interface InstanceExtra {
  bandwidth: number;
  cpu_max: number;
  cpu_model: string;
  features: number;
  gpu: number;
  gpu_class: number;
  gpu_pci_nums: string;
  hypervisor: string;
  iops: number;
  ivshmem: [];
  mem_max: number;
  nic_mqueue: number;
  os_disk_encryption: number;
  os_disk_size: number;
  read_iops: number;
  read_throughput: number;
  throughput: number;
  sanc_rep_count?: number;
}

export interface Instance {
  alarm_status: string;
  broker_host: null;
  broker_port: null;
  console_id: string;
  container_conf: unknown;
  controller: string;
  cpu_model_name: string;
  cpu_oversale_rate: number;
  cpu_topology: string;
  create_time: string;
  description: null;
  dns_aliases: [];
  eip: Eip;
  eips: Eip[];
  extra: InstanceExtra;
  fence: null;
  graphics_passwd: string;
  graphics_port: string;
  graphics_protocol: string;
  host_machine: string;
  hostname: string;
  instance_class_feature: number;
  image: Image;
  instance_class: number;
  instance_group: null;
  instance_id: string;
  instance_name: string;
  instance_type: string;
  keypair_ids: string[];
  lastest_snapshot_time: string;
  loadbalancer_ids: string;
  memory_current: number;
  memory_max: number;
  owner: string;
  pitrix_instance_type: string;
  place_group_id: string;
  place_group_name: string;
  platform: string;
  ratio: number;
  repl: string;
  resource_project_info: ResourceProjectInfo[];
  root_user_id: string;
  security_group: {
    is_default: 0 | 1;
    security_group_id: string;
    security_group_name: string;
  };
  security_groups: {
    is_default: 0 | 1;
    security_group_id: string;
    security_group_name: string;
  }[];
  status: string;
  status_time: string;
  sub_code: number;
  support_online?: boolean;
  tags: {
    color: string;
    owner: string;
    tag_id: string;
    tag_key: string;
    tag_name: string;
    tag_value: string;
  }[];
  transition_status: string;
  vcpus_current: number;
  vcpus_max: number;
  volume_ids: [];
  volumes: BaseVolume[];
  vxnets: {
    private_ip: string;
    vxnet_type: number;
    vxnet_id: string;
  }[];
  webssh: number;
  zone_id: string;
  cdrom_status: number;
  mount_image: {
    image_id: string;
    image_name: string;
    image_type: number;
  };
  mount_status: number;
  is_able_local_snapshot?: number;
}

/**
 * 主机类型
 */
export interface InstanceType {
  baremetal: boolean;
  cpu_model: string;
  extra_info?: {
    gpu_model: string;
    cpu_model: string;
    gpu_count: number;
  };
  features: number;
  gpu_class: number;
  gpu_count: number;
  gpu_model: string;
  instance_class: number;
  instance_class_feature: number;
  instance_style: string;
  instance_type_id: string;
  memory_current: number;
  memory_max: number;
  ratio: number;
  region_id: number;
  resource_class: number;
  resource_qos?: {
    bandwidth: number;
  };
  resource_type: string;
  status: string;
  vcpus_current: number;
  vcpus_max: number;
  zone_id: string;
  tag?: string;
  hypervisor: string;
  _isDisabled?: boolean;
  format_file_system?: boolean;
}

export interface InstanceTypeInfo {
  encryption: boolean;
  features: number | undefined;
  format_file_system: boolean;
  image_feature: number | undefined;
  image_market: boolean;
  instance_class: number | undefined;
  instance_class_feature: number | undefined;
  instance_group: boolean;
  nic_mqueue: boolean;
  os_family: string;
  platform: string;
}

export interface PlaceGroup {
  architectures: string;
  cpu_oversale_rate: number;
  create_time: string;
  disk_reserve_rate: number;
  is_excluded: number;
  mem_oversale_rate: number;
  place_group_id: string;
  place_group_name: string;
  status: string;
  status_time: string;
  visible: string;
  label: string;
  value: string;
}

export interface InstanceStyle {
  [key: string]: string[];
}

export interface InstanceStyleOption {
  label: string;
  value: string;
  resourceClass: string[];
  description: string;
  name: string;
  tooltip: {
    content: string;
  };
}

export interface Aliase {
  alias_name: string;
  create_time: Date;
  key_id: string;
}

export interface InstanceGroup {
  controller: string;
  instance_group_id: string;
  instance_group_name: string;
  resource_project_info: ResourceProjectInfo[];
  owner: string;
  place_group_name: string;
  status: string;
  relation: string;
  root_user_id: string;
  tags: {
    color: string;
    owner: string;
    tag_id: string;
    tag_key: string;
    tag_name: string;
    tag_value: string;
  }[];
}
