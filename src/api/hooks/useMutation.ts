import { useMutation, UseMutationOptions, useQueryClient, QueryKey } from 'react-query';
import { BaseRequest, BaseResponse } from '../types/common';

/**
 * 创建变更hook的工厂函数
 * @param apiFunc API函数
 * @param invalidateQueries 需要使失效的查询键数组
 * @returns 变更hook
 */
// eslint-disable-next-line import/prefer-default-export
export function createMutationHook<Req extends BaseRequest, <PERSON><PERSON> extends BaseResponse>(
  apiFunc: (params: Req) => Promise<Res>,
) {
  return (
    options?: UseMutationOptions<Res, unknown, Req> & {
      // 额外需要刷新的查询键
      invalidateQueries?: QueryKey[];
    },
  ) => {
    const queryClient = useQueryClient();
    const { invalidateQueries, ...restOptions } = options || {};

    return useMutation((params: Req) => apiFunc(params), {
      onSuccess: (data, variables, context) => {
        // 合并所有需要刷新的查询键
        const allInvalidateQueries = invalidateQueries || [];

        // 刷新相关查询
        if (allInvalidateQueries.length > 0) {
          allInvalidateQueries.forEach((key) => {
            queryClient.invalidateQueries(key);
          });
        }

        // 调用用户提供的onSuccess回调
        if (restOptions.onSuccess) {
          restOptions.onSuccess(data, variables, context);
        }
      },

      ...restOptions,
    });
  };
}
