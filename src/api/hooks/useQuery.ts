/**
 * React Query Hook 工厂函数
 * 简单、类型安全、功能完整
 */

import { useQuery, UseQueryOptions, QueryKey } from 'react-query';
import { useRef, useEffect } from 'react';
import { BaseRequest, BaseResponse } from '../types/common';

/**
 * 创建 React Query Hook 的工厂函数
 *
 * @param apiFunc API 调用函数
 * @param getQueryKey 生成查询键的函数
 * @param baseTransform 基础数据转换函数（可选）
 * @returns Hook 函数
 */
// eslint-disable-next-line import/prefer-default-export
export function createQueryHook<
  TParams extends BaseRequest,
  TResponse extends BaseResponse,
  TBaseData = TResponse,
  TKey extends QueryKey = QueryKey,
>(
  apiFunc: (params: TParams) => Promise<TResponse>,
  getQueryKey: (params: TParams) => TKey,
  baseTransform?: (response: TResponse) => TBaseData,
) {
  return function useQueryHook<TSelectData = TBaseData>(
    params: TParams,
    options?: {
      // 数据选择器，基于基础转换后的数据进行进一步处理
      select?: (data: TBaseData) => TSelectData;
      // 数据更新回调，每次数据变化都会调用（不包括缓存切换）
      onDataChange?: (data: TSelectData) => void;
      // 接口调用前的回调函数
      onBeforeFetch?: () => void;
    } & Omit<UseQueryOptions<TResponse, unknown, TSelectData, TKey>, 'queryKey' | 'queryFn' | 'select'>,
  ) {
    const { select, onDataChange, onBeforeFetch, ...queryOptions } = options || {};

    // 用于跟踪上一次的数据更新时间
    const prevUpdateKey = useRef<number>(0);

    const fetchFn = async () => {
      await onBeforeFetch?.();
      return apiFunc(params);
    };

    const queryResult = useQuery<TResponse, unknown, TSelectData, TKey>(getQueryKey(params), () => fetchFn(), {
      // 用户配置
      ...queryOptions,

      // 数据转换链：原始响应 -> 基础转换 -> 用户选择器
      select: (response: TResponse) => {
        const baseData = baseTransform ? baseTransform(response) : (response as unknown as TBaseData);
        return select ? select(baseData) : (baseData as unknown as TSelectData);
      },
    });

    // 监听数据变化，无论是网络请求还是缓存都会触发
    useEffect(() => {
      if (queryResult.isFetched) {
        const currentData = queryResult.dataUpdatedAt;
        const prevData = prevUpdateKey.current;
        if (currentData !== prevData) {
          onDataChange?.(queryResult?.data as unknown as TSelectData);
          prevUpdateKey.current = queryResult.dataUpdatedAt;
        }
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [queryResult.data, queryResult.isFetched, queryResult.dataUpdatedAt]);

    return queryResult;
  };
}
