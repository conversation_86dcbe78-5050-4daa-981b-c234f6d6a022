import { useRef, useEffect } from 'react';
import { useInfiniteQuery, UseInfiniteQueryOptions, QueryKey } from 'react-query';
import { BaseRequest, BaseResponse } from '../types/common';

/**
 * 创建 React Query 无限查询 Hook 的工厂函数
 *
 * @param apiFunc API 调用函数（需要支持分页参数）
 * @param getQueryKey 生成查询键的函数
 * @param baseTransform 基础数据转换函数（处理接口返回类型）
 * @param getNextPageParam 获取下一页参数的函数（可选）
 * @returns Hook 函数
 */
// eslint-disable-next-line import/prefer-default-export
export function createInfiniteQueryHook<
  TParams extends BaseRequest,
  TResponse extends BaseResponse,
  TBaseData,
  TKey extends QueryKey = QueryKey,
>(
  apiFunc: (params: TParams & { offset: number }) => Promise<TResponse>,
  getQueryKey: (params: Omit<TParams, 'offset'>) => TKey,
  baseTransform: (response: TResponse) => { items: TBaseData[]; total: number },
  getNextPageParam?: (
    lastPage: TResponse,
    allPages: TResponse[],
    params: Omit<TParams, 'offset'>,
  ) => number | undefined,
) {
  return function useInfiniteQueryHook<TSelectData = TBaseData[]>(
    params: Omit<TParams, 'offset'>,
    options?: {
      // 数据选择器，基于所有页面的数据进行处理
      select?: (allItems: TBaseData[]) => TSelectData;
      // 数据更新回调，每次数据变化都会调用
      onDataChange?: (data: TSelectData) => void;
      // 接口调用前的回调函数
      onBeforeFetch?: () => void;
      // 是否自动加载所有数据
      fetchAll?: boolean;
    } & Omit<
      UseInfiniteQueryOptions<TResponse, unknown, TResponse, TResponse, TKey>,
      'queryKey' | 'queryFn' | 'getNextPageParam' | 'select'
    >,
  ) {
    const { select, onDataChange, onBeforeFetch, ...queryOptions } = options || {};

    const prevUpdateKey = useRef<number>(0);

    // 默认的获取下一页参数函数
    const defaultGetNextPageParam = (lastPage: TResponse, allPages: TResponse[]) => {
      const { total } = baseTransform(lastPage);
      const limit = ((params as Record<string, unknown>).limit as number) || 20;
      const currentOffset = allPages.length * limit;
      return currentOffset < total ? currentOffset : undefined;
    };

    const fetchFn = async ({ pageParam = 0 }) => {
      await onBeforeFetch?.();
      return apiFunc({ ...params, offset: pageParam } as TParams & { offset: number });
    };

    const queryResult = useInfiniteQuery<TResponse, unknown, TResponse, TKey>(getQueryKey(params), fetchFn, {
      // 用户配置
      ...queryOptions,

      // 获取下一页参数
      getNextPageParam: getNextPageParam
        ? (lastPage, allPages) => getNextPageParam(lastPage, allPages, params)
        : defaultGetNextPageParam,
    });

    // 处理数据转换和选择
    const processedData = queryResult.data
      ? (() => {
          // 将所有页面的数据合并为一个数组
          const allItems = queryResult.data.pages.flatMap((page) => baseTransform(page).items);
          // 应用用户的选择器
          return select ? select(allItems) : (allItems as unknown as TSelectData);
        })()
      : undefined;

    useEffect(() => {
      if (queryResult.isFetched) {
        const currentData = queryResult.dataUpdatedAt;
        const prevData = prevUpdateKey.current;
        if (currentData !== prevData) {
          onDataChange?.(processedData as unknown as TSelectData);
          prevUpdateKey.current = queryResult.dataUpdatedAt;
        }
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [queryResult.data, queryResult.isFetched, queryResult.dataUpdatedAt]);

    useEffect(() => {
      if (options?.fetchAll && queryResult.hasNextPage && !queryResult.isFetchingNextPage) {
        queryResult.fetchNextPage();
      }
    }, [options?.fetchAll, queryResult.hasNextPage, queryResult.fetchNextPage, queryResult]);

    // 返回增强的查询结果
    return {
      ...queryResult,
      // 覆盖 data 为处理后的数据
      data: processedData,
      // 添加一些有用的辅助属性
      allItems: processedData,
      totalCount: queryResult.data?.pages[0] ? baseTransform(queryResult.data.pages[0]).total : 0,
      loadedCount: queryResult.data?.pages.reduce((total, page) => total + baseTransform(page).items.length, 0) || 0,
      hasMore: !!queryResult.hasNextPage,
      loadMore: queryResult.fetchNextPage,
      isLoadingMore: queryResult.isFetchingNextPage,
    };
  };
}
