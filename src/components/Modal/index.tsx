// 导出组件
// 重新导入用于默认导出
import {
  showFormModal,
  showConfirmModal,
  showModal,
  hideModal,
  removeModal,
  registerModal,
  createDeleteConfirm,
  createInfoModal,
  createSuccessModal,
  createWarningModal,
  createErrorModal,
  createInputModal,
} from './utils';

import { BaseModal } from './components/BaseModal';
import { FormModal } from './components/FormModal';
import { ConfirmModal } from './components/ConfirmModal';
import { ModalHeader } from './components/ModalHeader';

// 导出类型
export type {
  BaseModalConfig,
  FormModalConfig,
  ConfirmModalConfig,
  ModalInstance,
  ModalReturn,
  PresetModalConfig,
  FormFieldConfig,
  QuickFormConfig,
  ButtonType,
  ConfirmType,
} from './types';

// 导出常量
export {
  CONFIRM_TYPE_MAP,
  DEFAULT_CONFIG,
  Z_INDEX,
  ANIMATION_CONFIG,
  SIZE_PRESETS,
  FIELD_TYPE_MAP,
  VALIDATION_RULES,
} from './constants';

// 导出工具函数
export {
  showFormModal,
  showConfirmModal,
  showModal,
  hideModal,
  removeModal,
  registerModal,
  createDeleteConfirm,
  createInfoModal,
  createSuccessModal,
  createWarningModal,
  createErrorModal,
  createInputModal,
  // 简化的别名
  form,
  confirm,
  deleteConfirm,
  info,
  success,
  warning,
  error,
  input,
} from './utils';

// 默认导出一个包含所有方法的对象
const Modal = {
  // 组件
  Base: BaseModal,
  Form: FormModal,
  Confirm: ConfirmModal,
  Header: ModalHeader,

  // 方法
  show: showModal,
  form: showFormModal,
  confirm: showConfirmModal,
  hide: hideModal,
  remove: removeModal,
  register: registerModal,

  // 预设方法
  deleteConfirm: createDeleteConfirm,
  info: createInfoModal,
  success: createSuccessModal,
  warning: createWarningModal,
  error: createErrorModal,
  input: createInputModal,
};

export default Modal;
