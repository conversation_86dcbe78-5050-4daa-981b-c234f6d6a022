import React, { useEffect, useState, useCallback } from 'react';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import {
  useModal as useUiModal,
  ModalBody,
  ModalFooter,
} from '@pitrix/portal-ui';
import { BaseModalConfig } from '../types';
import { DEFAULT_CONFIG } from '../constants';
import { ModalHeader } from './ModalHeader';

interface BaseModalProps extends BaseModalConfig {
  /** 确定按钮点击回调 */
  onOk?: () => void | Promise<void>;
  /** 取消按钮点击回调 */
  onCancel?: () => void;
  /** 关闭弹窗回调 */
  onClose?: () => void;
}

export const BaseModal = NiceModal.create<BaseModalProps>(({
  title,
  width = DEFAULT_CONFIG.width,
  children,
  loading = DEFAULT_CONFIG.loading,
  disabled = DEFAULT_CONFIG.disabled,
  okText = DEFAULT_CONFIG.okText,
  cancelText = DEFAULT_CONFIG.cancelText,
  okType = DEFAULT_CONFIG.okType,
  cancelType = DEFAULT_CONFIG.cancelType,
  hideCancel = DEFAULT_CONFIG.hideCancel,
  noFooter = DEFAULT_CONFIG.noFooter,
  maskClosable = DEFAULT_CONFIG.maskClosable,
  destroyOnClose = DEFAULT_CONFIG.destroyOnClose,
  bordered = DEFAULT_CONFIG.bordered,
  onOk,
  onCancel,
  onClose,
}) => {
  const modal = useModal();
  const [submitLoading, setSubmitLoading] = useState(false);

  // 处理关闭
  function handleClose() {
    onClose?.();
    onCancel?.();
    modal.reject(new Error('User cancelled'));
    modal.hide();
  }

  // 使用 @pitrix/portal-ui 的 useModal
  const { Modal, open, close } = useUiModal({
    maskClosable,
    onClose: handleClose,
  });

  // 同步 NiceModal 状态
  useEffect(() => {
    if (modal.visible) {
      open();
    } else {
      close();
    }
  }, [modal.visible, open, close]);

  // 处理确定
  const handleOk = useCallback(async () => {
    try {
      if (onOk) {
        const result = onOk();
        if (result instanceof Promise) {
          setSubmitLoading(true);
          await result;
          setSubmitLoading(false);
        }
      }
      modal.resolve();
      modal.hide();
    } catch (error) {
      setSubmitLoading(false);
      modal.reject(error);
      // 不自动关闭，让用户处理错误
    }
  }, [onOk, modal]);

  // 销毁时重置状态
  useEffect(() => {
    if (!modal.visible && destroyOnClose) {
      setSubmitLoading(false);
    }
  }, [modal.visible, destroyOnClose]);

  return (
    <Modal
      css={{
        width,
        _selector: !bordered
          ? {
              selector: '&>div:nth-child(odd)',
              border: 'none',
            }
          : undefined,
      }}
    >
      <ModalHeader
        title={title}
        onClose={handleClose}
        bordered={bordered}
      />
      <ModalBody css={bordered ? {} : { padding: '0 20px' }}>
        {children}
      </ModalBody>
      {!noFooter && (
        <ModalFooter
          btns={[
            ...(!hideCancel ? [{
              key: 'cancel',
              type: cancelType,
              children: cancelText,
              onClick: handleClose,
              css: { outline: 'none' },
            }] : []),
            {
              key: 'ok',
              type: okType,
              children: okText,
              loading: loading || submitLoading,
              disabled,
              onClick: handleOk,
              css: { outline: 'none' },
            },
          ]}
        />
      )}
    </Modal>
  );
});

export default BaseModal;
