import React, { useEffect, useState, useCallback } from 'react';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { useForm, FormProvider, FieldValues, SubmitHandler } from 'react-hook-form';
import {
  useModal as useUiModal,
  Modal<PERSON><PERSON>,
  ModalFooter,
} from '@pitrix/portal-ui';
import { FormModalConfig } from '../types';
import { DEFAULT_CONFIG } from '../constants';
import { ModalHeader } from './ModalHeader';

interface FormModalProps<T extends FieldValues = FieldValues> extends FormModalConfig<T> {
  /** 关闭弹窗回调 */
  onClose?: () => void;
}

export const FormModal = NiceModal.create(<T extends FieldValues = FieldValues>({
  title,
  width = DEFAULT_CONFIG.width,
  children,
  loading = DEFAULT_CONFIG.loading,
  disabled = DEFAULT_CONFIG.disabled,
  okText = DEFAULT_CONFIG.okText,
  cancelText = DEFAULT_CONFIG.cancelText,
  okType = DEFAULT_CONFIG.okType,
  cancelType = DEFAULT_CONFIG.cancelType,
  hideCancel = DEFAULT_CONFIG.hideCancel,
  noFooter = DEFAULT_CONFIG.noFooter,
  maskClosable = DEFAULT_CONFIG.maskClosable,
  destroyOnClose = DEFAULT_CONFIG.destroyOnClose,
  bordered = DEFAULT_CONFIG.bordered,
  defaultValues,
  formOptions,
  onOk,
  onAsyncOk,
  onCancel,
  onClose,
}: FormModalProps<T>) => {
  const modal = useModal();
  const [submitLoading, setSubmitLoading] = useState(false);

  // 初始化 react-hook-form
  const methods = useForm<T>({
    defaultValues: defaultValues as any,
    ...formOptions,
  });

  // 使用 @pitrix/portal-ui 的 useModal
  const { Modal, open, close } = useUiModal({
    maskClosable,
    // onClose: handleClose,
  });

  // 处理关闭
  function handleClose() {
    onClose?.();
    onCancel?.();
    modal.hide();
  }

  // 同步 NiceModal 状态
  useEffect(() => {
    if (modal.visible) {
      open();
      // 重置表单
      if (defaultValues) {
        methods.reset(defaultValues);
      }
    } else {
      close();
    }
  }, [modal.visible, open, close, methods, defaultValues]);

  // 处理表单提交
  const handleSubmit: SubmitHandler<T> = useCallback(async (data) => {
    try {
      if (onAsyncOk) {
        setSubmitLoading(true);
        await onAsyncOk(data);
        setSubmitLoading(false);
      }
      if (onOk) {
        onOk(data);
      }
      modal.resolve(data);
      modal.hide();
    } catch (error) {
      setSubmitLoading(false);
      modal.reject(error);
      // 不自动关闭，让用户处理错误
    }
  }, [onOk, onAsyncOk, modal]);

  // 处理确定按钮点击
  const handleOk = useCallback(() => {
    methods.handleSubmit(handleSubmit)();
  }, [methods, handleSubmit]);

  // 销毁时重置状态
  useEffect(() => {
    if (!modal.visible && destroyOnClose) {
      setSubmitLoading(false);
      methods.reset();
    }
  }, [modal.visible, destroyOnClose, methods]);

  // 计算按钮禁用状态
  const isDisabled = typeof disabled === 'function'
    ? (disabled as any)(methods.watch())
    : disabled;

  return (
    <Modal
      css={{
        width,
        _selector: !bordered
          ? {
              selector: '&>div:nth-child(odd)',
              border: 'none',
            }
          : undefined,
      }}
    >
      <FormProvider {...methods}>
        <ModalHeader
          title={title}
          onClose={handleClose}
          bordered={bordered}
        />
        <ModalBody css={bordered ? {} : { padding: '0 20px' }}>
          {children}
        </ModalBody>
        {!noFooter && (
          <ModalFooter
            btns={[
              ...(!hideCancel ? [{
                key: 'cancel',
                type: cancelType,
                children: cancelText,
                onClick: handleClose,
                css: { outline: 'none' },
              }] : []),
              {
                key: 'ok',
                type: okType,
                children: okText,
                loading: loading || submitLoading,
                disabled: isDisabled,
                onClick: handleOk,
                css: { outline: 'none' },
              },
            ]}
          />
        )}
      </FormProvider>
    </Modal>
  );
});

export default FormModal;
