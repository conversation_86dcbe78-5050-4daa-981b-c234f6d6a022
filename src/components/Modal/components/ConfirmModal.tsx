import React, { useEffect, useState, useCallback } from 'react';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import {
  useModal as useUiModal,
  ModalBody,
  ModalFooter,
  portal,
  Icon,
} from '@pitrix/portal-ui';
import { ConfirmModalConfig } from '../types';
import { DEFAULT_CONFIG, CONFIRM_TYPE_MAP } from '../constants';
import { ModalHeader } from './ModalHeader';

interface ConfirmModalProps extends ConfirmModalConfig {
  /** 关闭弹窗回调 */
  onClose?: () => void;
}

export const ConfirmModal = NiceModal.create(({
  title,
  width = 400,
  children,
  content,
  type = 'info',
  loading = DEFAULT_CONFIG.loading,
  disabled = DEFAULT_CONFIG.disabled,
  okText = DEFAULT_CONFIG.okText,
  cancelText = DEFAULT_CONFIG.cancelText,
  okType,
  cancelType = DEFAULT_CONFIG.cancelType,
  hideCancel = DEFAULT_CONFIG.hideCancel,
  noFooter = DEFAULT_CONFIG.noFooter,
  maskClosable = DEFAULT_CONFIG.maskClosable,
  destroyOnClose = DEFAULT_CONFIG.destroyOnClose,
  bordered = DEFAULT_CONFIG.bordered,
  onOk,
  onAsyncOk,
  onCancel,
  onClose,
}: ConfirmModalProps) => {
  const modal = useModal();
  const [submitLoading, setSubmitLoading] = useState(false);

  // 获取确认类型配置
  const typeConfig = CONFIRM_TYPE_MAP[type];
  const finalOkType = okType || typeConfig.buttonType;

  // 处理关闭
  function handleClose() {
    onClose?.();
    onCancel?.();
    modal.remove();
  }

  // 使用 @pitrix/portal-ui 的 useModal
  const { Modal, open, close } = useUiModal({
    maskClosable,
    onClose: handleClose,
  });

  // 同步 NiceModal 状态
  useEffect(() => {
    if (modal.visible) {
      open();
    } else {
      close();
    }
  }, [modal.visible, open, close]);

  // 处理确定
  const handleOk = useCallback(async () => {
    try {
      if (onAsyncOk) {
        setSubmitLoading(true);
        await onAsyncOk();
        setSubmitLoading(false);
      }
      if (onOk) {
        onOk();
      }
      modal.resolve();
      modal.hide();
    } catch (error) {
      setSubmitLoading(false);
      modal.reject(error);
      // 不自动关闭，让用户处理错误
    }
  }, [onOk, onAsyncOk, modal]);

  // 销毁时重置状态
  useEffect(() => {
    if (!modal.visible && destroyOnClose) {
      setSubmitLoading(false);
    }
  }, [modal.visible, destroyOnClose]);

  return (
    <Modal
      css={{
        width,
        _selector: !bordered
          ? {
            selector: '&>div:nth-child(odd)',
            border: 'none',
          }
          : undefined,
      }}
    >
      <ModalHeader
        title={title}
        onClose={handleClose}
        bordered={bordered}
      />
      <ModalBody css={{ padding: '24px 20px' }}>
        <portal.div css={{ display: 'flex', alignItems: 'flex-start' }}>
          <portal.div css={{ color: typeConfig.color as any, display: 'flex', marginRight: '12px' }}>
            <Icon name={typeConfig.icon} size={22} />
          </portal.div>
          <portal.div css={{ flex: 1 }}>
            {title && (
              <portal.div css={{
                fontSize: 'l',
                fontWeight: 'medium',
                marginBottom: '8px',
                color: 'text.headerBase'
              }}>
                {title}
              </portal.div>
            )}
            {content && (
              <portal.div css={{
                color: 'text.placeholder',
                lineHeight: '1.5'
              }}>
                {content}
              </portal.div>
            )}
            {children && (
              <portal.div css={{ marginTop: content ? '8px' : '0' }}>
                {children}
              </portal.div>
            )}
          </portal.div>
        </portal.div>
      </ModalBody>
      {!noFooter && (
        <ModalFooter
          btns={[
            ...(!hideCancel ? [{
              key: 'cancel',
              type: cancelType,
              children: cancelText,
              onClick: handleClose,
              css: { outline: 'none' },
            }] : []),
            {
              key: 'ok',
              type: finalOkType,
              children: okText,
              loading: loading || submitLoading,
              disabled,
              onClick: handleOk,
              css: { outline: 'none' },
            },
          ]}
        />
      )}
    </Modal>
  );
});

export default ConfirmModal;
