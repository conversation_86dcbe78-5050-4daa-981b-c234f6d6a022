import React from 'react';
import { portal, StyleProps, Icon } from '@pitrix/portal-ui';

const modalHeaderStyle: StyleProps = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  height: '56px',
  lineHeight: '56px',
  paddingLeft: '20px',
  paddingRight: '20px',
  borderBottom: 'neutral.2',
  '& .q-icon:hover': {
    backgroundColor: 'neutral.2',
  },
};

const modalHeaderStyleNoBorder: StyleProps = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  height: '40px',
  paddingLeft: '20px',
  paddingRight: '20px',
  '& .q-icon:hover': {
    backgroundColor: 'neutral.2',
  },
};

const titleStyle: StyleProps = {
  flexGrow: '1',
  minWidth: '0',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  textHeader: '4',
  color: 'text.headerBase',
};

interface ModalHeaderProps {
  title?: string;
  onClose?: () => void;
  bordered?: boolean;
}

export const ModalHeader = ({
  title,
  onClose,
  bordered = true
}: ModalHeaderProps): JSX.Element => {
  return (
    <portal.div
      css={bordered ? modalHeaderStyle : modalHeaderStyleNoBorder}
      style={{ fontSize: '16px' }}
    >
      <portal.span css={titleStyle}>{title}</portal.span>
      <Icon
        name="close-fill"
        size={20}
        style={{
          marginLeft: '20px',
          cursor: 'pointer',
          borderRadius: '2px',
          transition: 'all .2s ease',
        }}
        onClick={onClose}
      />
    </portal.div>
  );
};

export default ModalHeader;
