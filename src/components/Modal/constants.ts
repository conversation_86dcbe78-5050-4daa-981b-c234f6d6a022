import { ConfirmType, ButtonType } from './types';

// 确认弹窗类型映射
export const CONFIRM_TYPE_MAP: Record<ConfirmType, {
  icon: string;
  color: string;
  buttonType: ButtonType;
}> = {
  info: {
    icon: 'information-fill',
    color: 'blue.6',
    buttonType: 'primary',
  },
  success: {
    icon: 'check-circle-fill',
    color: 'green.6',
    buttonType: 'primary',
  },
  warning: {
    icon: 'alert-triangle-fill',
    color: 'orange.6',
    buttonType: 'danger',
  },
  error: {
    icon: 'close-circle-fill',
    color: 'red.6',
    buttonType: 'danger',
  },
};

// 默认配置
export const DEFAULT_CONFIG = {
  width: 500,
  okText: '确定',
  cancelText: '取消',
  okType: 'primary' as ButtonType,
  cancelType: 'normal' as ButtonType,
  maskClosable: false,
  destroyOnClose: true,
  bordered: true,
  hideCancel: false,
  noFooter: false,
  loading: false,
  disabled: false,
};

// Z-Index 层级
export const Z_INDEX = {
  MODAL: 1000,
  MODAL_MASK: 999,
};

// 动画配置
export const ANIMATION_CONFIG = {
  duration: 200,
  easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
};

// 尺寸预设
export const SIZE_PRESETS = {
  small: 400,
  medium: 520,
  large: 720,
  extraLarge: 960,
};

// 表单字段类型映射
export const FIELD_TYPE_MAP = {
  input: 'text',
  textarea: 'textarea',
  select: 'select',
  checkbox: 'checkbox',
  radio: 'radio',
  number: 'number',
  email: 'email',
  password: 'password',
  url: 'url',
  tel: 'tel',
  date: 'date',
  datetime: 'datetime-local',
  time: 'time',
};

// 验证规则预设
export const VALIDATION_RULES = {
  required: { required: '此字段为必填项' },
  email: {
    pattern: {
      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
      message: '请输入有效的邮箱地址',
    },
  },
  phone: {
    pattern: {
      value: /^1[3-9]\d{9}$/,
      message: '请输入有效的手机号码',
    },
  },
  url: {
    pattern: {
      value: /^https?:\/\/.+/,
      message: '请输入有效的URL地址',
    },
  },
  minLength: (length: number) => ({
    minLength: {
      value: length,
      message: `最少输入${length}个字符`,
    },
  }),
  maxLength: (length: number) => ({
    maxLength: {
      value: length,
      message: `最多输入${length}个字符`,
    },
  }),
  min: (value: number) => ({
    min: {
      value,
      message: `最小值为${value}`,
    },
  }),
  max: (value: number) => ({
    max: {
      value,
      message: `最大值为${value}`,
    },
  }),
};
