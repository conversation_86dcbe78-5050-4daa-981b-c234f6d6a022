import React from 'react';
import { useForm<PERSON>ontext, Controller } from 'react-hook-form';
import { Button, Input } from '@pitrix/portal-ui';
import Modal, {
  showFormModal,
  showConfirmModal,
  createDeleteConfirm,
  createInfoModal,
  createWarningModal,
  createSuccessModal,
  createErrorModal,
} from './index';

// 基础表单内容
const BasicFormContent = () => {
  const { control, formState: { errors } } = useFormContext<{
    name: string;
    email: string;
  }>();

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', padding: '20px 0' }}>
      <Controller
        name="name"
        control={control}
        rules={{ required: '请输入姓名' }}
        render={({ field }) => (
          <div>
            <label style={{ display: 'block', marginBottom: '4px' }}>姓名</label>
            <Input {...field} placeholder="请输入姓名" />
            {errors.name && (
              <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>
                {errors.name.message}
              </div>
            )}
          </div>
        )}
      />

      <Controller
        name="email"
        control={control}
        rules={{
          required: '请输入邮箱',
          pattern: {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: '请输入有效的邮箱地址',
          },
        }}
        render={({ field }) => (
          <div>
            <label style={{ display: 'block', marginBottom: '4px' }}>邮箱</label>
            <Input {...field} placeholder="请输入邮箱" />
            {errors.email && (
              <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>
                {errors.email.message}
              </div>
            )}
          </div>
        )}
      />
    </div>
  );
};

// 基础表单示例
export const BasicFormExample = () => {
  const handleShowForm = async () => {
    try {
      const result = await showFormModal<{ name: string; email: string }>({
        title: '用户信息',
        width: 500,
        defaultValues: {
          name: '',
          email: '',
        },
        children: <BasicFormContent />,
        onAsyncOk: async (data) => {
          // 模拟异步提交
          await new Promise<void>((resolve) => setTimeout(() => resolve(), 500));
          // eslint-disable-next-line no-console
          console.log('提交的数据:', data);
        },
      }).then((res) => {
        console.log('cheng', res);
        return res;
      });

      // eslint-disable-next-line no-console
      console.log('表单结果:', result);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('用户取消或出错:', error);
    }
  };

  return (
    <Button onClick={handleShowForm}>
      显示表单弹窗
    </Button>
  );
};

// 确认弹窗示例
export const ConfirmExample = () => {
  const handleShowConfirm = async () => {
    try {
      await showConfirmModal({
        title: '确认操作',
        content: '确定要执行此操作吗？',
        onAsyncOk: async () => {
          await new Promise<void>((resolve) => setTimeout(() => resolve(), 1000));
          // eslint-disable-next-line no-console
          console.log('操作已确认');
        },
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('用户取消');
    }
  };

  return (
    <Button onClick={handleShowConfirm}>
      显示确认弹窗
    </Button>
  );
};

// 删除确认示例
export const DeleteConfirmExample = () => {
  const handleDelete = async () => {
    try {
      await createDeleteConfirm({
        title: '删除用户',
        content: '确定要删除该用户吗？删除后无法恢复。',
        onConfirm: async () => {
          await new Promise<void>((resolve) => setTimeout(resolve, 1000));
          // eslint-disable-next-line no-console
          console.log('用户已删除');
        },
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('取消删除');
    }
  };

  return (
    <Button type="danger" onClick={handleDelete}>
      删除用户
    </Button>
  );
};

// 信息提示示例
export const InfoExample = () => {
  const handleShowInfo = async () => {
    await createInfoModal({
      title: '操作成功',
      content: '您的操作已成功完成！',
    });
  };

  return (
    <Button onClick={handleShowInfo}>
      显示信息提示
    </Button>
  );
};

// 成功提示示例
export const SuccessExample = () => {
  const handleShowSuccess = async () => {
    await createSuccessModal({
      title: '保存成功',
      content: '数据已成功保存到服务器！',
    });
  };

  return (
    <Button onClick={handleShowSuccess}>
      显示成功提示
    </Button>
  );
};

// 警告示例
export const WarningExample = () => {
  const handleShowWarning = async () => {
    try {
      await createWarningModal({
        title: '警告',
        content: '此操作可能会影响系统性能，确定要继续吗？',
        onConfirm: async () => {
          await new Promise((resolve) => setTimeout(resolve, 1000));
          // eslint-disable-next-line no-console
          console.log('用户确认了警告操作');
        },
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('用户取消了警告操作');
    }
  };

  return (
    <Button type="danger" onClick={handleShowWarning}>
      显示警告弹窗
    </Button>
  );
};

// 错误示例
export const ErrorExample = () => {
  const handleShowError = async () => {
    await createErrorModal({
      title: '操作失败',
      content: '网络连接异常，请检查网络设置后重试。',
    });
  };

  return (
    <Button onClick={handleShowError}>
      显示错误提示
    </Button>
  );
};

// 使用 Modal 对象的示例
export const ModalObjectExample = () => {
  const handleUseModalObject = async () => {
    try {
      const result = await Modal.form<{ message: string }>({
        title: '使用 Modal 对象',
        defaultValues: { message: 'Hello Modal!' },
        children: (
          <div style={{ padding: '20px 0' }}>
            <Controller
              name="message"
              render={({ field }) => (
                <Input {...field} placeholder="请输入消息" />
              )}
            />
          </div>
        ),
      });

      // eslint-disable-next-line no-console
      console.log('Modal 对象结果:', result);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('Modal 对象取消:', error);
    }
  };

  return (
    <Button onClick={handleUseModalObject}>
      使用 Modal 对象
    </Button>
  );
};

// 组合示例组件
export const ModalExamples = () => {
  return (
    <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <h2>Modal 组件使用示例</h2>

      <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
        <BasicFormExample />
        <ConfirmExample />
        <DeleteConfirmExample />
        <InfoExample />
        <SuccessExample />
        <WarningExample />
        <ErrorExample />
        <ModalObjectExample />
      </div>
    </div>
  );
};

export default ModalExamples;
