# Modal 组件使用指南

## 快速开始

### 1. 基础设置

确保在应用根组件包含 `NiceModal.Provider`：

```tsx
import NiceModal from '@ebay/nice-modal-react';

function App() {
  return (
    <NiceModal.Provider>
      {/* 您的应用内容 */}
    </NiceModal.Provider>
  );
}
```

### 2. 导入组件

```tsx
import Modal from 'components/Modal';
```

## 常用示例

### 表单弹窗

```tsx
const handleShowForm = async () => {
  try {
    const result = await Modal.form({
      title: '用户信息',
      defaultValues: { name: '', email: '' },
      children: <FormContent />,
      onAsyncOk: async (data) => {
        await saveUser(data);
      },
    });
    console.log('表单数据:', result);
  } catch (error) {
    console.log('用户取消');
  }
};
```

### 确认弹窗

```tsx
const handleDelete = async () => {
  try {
    await Modal.deleteConfirm({
      title: '删除确认',
      content: '确定要删除吗？',
      onConfirm: async () => {
        await deleteItem();
      },
    });
  } catch (error) {
    console.log('用户取消删除');
  }
};
```

### 信息提示

```tsx
// 成功提示
await Modal.success({
  title: '操作成功',
  content: '数据已保存',
});

// 错误提示
await Modal.error({
  title: '操作失败',
  content: '网络连接异常',
});

// 警告提示
await Modal.warning({
  title: '警告',
  content: '此操作不可逆',
  onConfirm: async () => {
    await performAction();
  },
});
```

## 表单组件示例

```tsx
import { Controller, useFormContext } from 'react-hook-form';
import { Input } from '@pitrix/portal-ui';

const FormContent = () => {
  const { control, formState: { errors } } = useFormContext();
  
  return (
    <div style={{ padding: '20px 0' }}>
      <Controller
        name="name"
        control={control}
        rules={{ required: '请输入姓名' }}
        render={({ field }) => (
          <div>
            <label>姓名</label>
            <Input {...field} placeholder="请输入姓名" />
            {errors.name && (
              <div style={{ color: 'red' }}>
                {errors.name.message}
              </div>
            )}
          </div>
        )}
      />
    </div>
  );
};
```

## API 参考

### Modal.form(config)
- `title`: 弹窗标题
- `width`: 弹窗宽度 (默认: 520)
- `defaultValues`: 表单默认值
- `children`: 表单内容组件
- `onAsyncOk`: 异步提交回调
- `onCancel`: 取消回调

### Modal.confirm(config)
- `title`: 弹窗标题
- `content`: 确认内容
- `type`: 确认类型 ('info' | 'success' | 'warning' | 'error')
- `onAsyncOk`: 异步确认回调
- `onCancel`: 取消回调

### 预设方法
- `Modal.deleteConfirm()`: 删除确认弹窗
- `Modal.info()`: 信息提示弹窗
- `Modal.success()`: 成功提示弹窗
- `Modal.warning()`: 警告弹窗
- `Modal.error()`: 错误提示弹窗

## 注意事项

1. **表单组件必须使用 `useFormContext`** 获取表单实例
2. **异步操作会自动显示 loading 状态**
3. **Promise reject 表示用户取消或操作失败**
4. **使用 TypeScript 泛型确保类型安全**

## 测试

运行测试组件：

```tsx
import { TestApp } from 'components/Modal/test';

// 在您的应用中渲染
<TestApp />
```
