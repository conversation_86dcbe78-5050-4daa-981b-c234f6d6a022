import NiceModal from '@ebay/nice-modal-react';
import { FieldValues } from 'react-hook-form';
import { FormModal } from './components/FormModal';
import { ConfirmModal } from './components/ConfirmModal';
import { BaseModal } from './components/BaseModal';
import {
  FormModalConfig,
  ConfirmModalConfig,
  PresetModalConfig,
  ModalReturn,
  ModalInstance,
} from './types';

// 创建 Modal 实例包装器
function createModalReturn<T = any>(promise: Promise<T>): ModalReturn {
  const modal: ModalInstance = {
    show: () => {
      // NiceModal 已经显示，这里可以添加额外逻辑
    },
    hide: () => {
      // 需要通过 NiceModal 的方式来隐藏
    },
    destroy: () => {
      // 需要通过 NiceModal 的方式来销毁
    },
    update: () => {
      // 需要通过 NiceModal 的方式来更新
    },
  };

  return Object.assign(promise, { modal });
}

/**
 * 显示表单弹窗
 * @param config 表单弹窗配置
 * @returns Promise，resolve 时返回表单数据，reject 时返回错误
 */
export function showFormModal<T extends FieldValues = FieldValues>(
  config: FormModalConfig<T>,
): ModalReturn {
  const promise = NiceModal.show(FormModal, config as any) as Promise<T>;
  return createModalReturn(promise);
}

/**
 * 显示确认弹窗
 * @param config 确认弹窗配置
 * @returns Promise，resolve 时表示确认，reject 时表示取消
 */
export function showConfirmModal(config: ConfirmModalConfig): ModalReturn {
  const promise = NiceModal.show(ConfirmModal, config) as Promise<void>;
  return createModalReturn(promise);
}

/**
 * 显示基础弹窗
 * @param config 基础弹窗配置
 * @returns Promise，resolve 时表示确认，reject 时表示取消
 */
export function showModal(config: any): ModalReturn {
  const promise = NiceModal.show(BaseModal, config) as Promise<any>;
  return createModalReturn(promise);
}

/**
 * 隐藏弹窗
 * @param modal 弹窗组件或ID
 */
export function hideModal(modal: any): void {
  NiceModal.hide(modal);
}

/**
 * 移除弹窗
 * @param modal 弹窗组件或ID
 */
export function removeModal(modal: any): void {
  NiceModal.remove(modal);
}

/**
 * 注册弹窗
 * @param id 弹窗ID
 * @param component 弹窗组件
 */
export function registerModal(id: string, component: any): void {
  NiceModal.register(id, component);
}

// 预设弹窗函数

/**
 * 创建删除确认弹窗
 */
export function createDeleteConfirm(config: PresetModalConfig): ModalReturn {
  return showConfirmModal({
    type: 'warning',
    title: config.title || '确认删除',
    content: config.content || '确定要删除吗？删除后无法恢复。',
    okText: '删除',
    okType: 'danger',
    onAsyncOk: config.onConfirm,
    onCancel: config.onCancel,
  });
}

/**
 * 创建信息提示弹窗
 */
export function createInfoModal(config: PresetModalConfig): ModalReturn {
  return showConfirmModal({
    type: 'info',
    title: config.title || '提示',
    content: config.content,
    hideCancel: true,
    onOk: config.onConfirm,
    onCancel: config.onCancel,
  });
}

/**
 * 创建成功提示弹窗
 */
export function createSuccessModal(config: PresetModalConfig): ModalReturn {
  return showConfirmModal({
    type: 'success',
    title: config.title || '成功',
    content: config.content,
    hideCancel: true,
    onOk: config.onConfirm,
    onCancel: config.onCancel,
  });
}

/**
 * 创建警告弹窗
 */
export function createWarningModal(config: PresetModalConfig): ModalReturn {
  return showConfirmModal({
    type: 'warning',
    title: config.title || '警告',
    content: config.content,
    okType: 'danger',
    onAsyncOk: config.onConfirm,
    onCancel: config.onCancel,
  });
}

/**
 * 创建错误弹窗
 */
export function createErrorModal(config: PresetModalConfig): ModalReturn {
  return showConfirmModal({
    type: 'error',
    title: config.title || '错误',
    content: config.content,
    hideCancel: true,
    okType: 'danger',
    onOk: config.onConfirm,
    onCancel: config.onCancel,
  });
}

/**
 * 创建输入弹窗
 */
export function createInputModal<T extends FieldValues = FieldValues>(
  config: FormModalConfig<T> & { placeholder?: string }
): ModalReturn {
  return showFormModal({
    title: config.title || '请输入',
    width: 400,
    ...config,
    children: config.children,
  });
}

// 导出所有函数
export {
  showFormModal as form,
  showConfirmModal as confirm,
  createDeleteConfirm as deleteConfirm,
  createInfoModal as info,
  createSuccessModal as success,
  createWarningModal as warning,
  createErrorModal as error,
  createInputModal as input,
};
