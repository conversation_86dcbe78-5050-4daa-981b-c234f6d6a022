# Modal 组件

基于 `@ebay/nice-modal-react`、`@pitrix/portal-ui` 和 `react-hook-form` 的现代化命令式弹窗解决方案。

## ✨ 特性

- 🚀 **命令式 API**: 通过函数调用显示弹窗，无需维护状态
- 📝 **表单集成**: 内置 react-hook-form 支持，完整的表单验证
- 🎨 **UI 一致性**: 使用 @pitrix/portal-ui 组件，保持设计系统一致性
- 💪 **TypeScript**: 完整的 TypeScript 支持，提供类型安全
- 🔄 **异步支持**: 支持异步操作，自动处理 loading 状态
- 🎯 **Promise 化**: 返回 Promise，支持 async/await 语法
- 🛠️ **预设弹窗**: 提供常用的确认、信息、警告、错误等预设弹窗
- 🎛️ **高度可配置**: 支持丰富的配置选项和自定义

## 📦 安装

组件已集成到项目中，无需额外安装。

## 🚀 快速开始

### 基础用法

```tsx
import Modal from 'components/Modal';

// 表单弹窗
const result = await Modal.form({
  title: '用户信息',
  defaultValues: { name: '', email: '' },
  children: <FormContent />,
});

// 确认弹窗
await Modal.confirm({
  title: '确认删除',
  content: '确定要删除吗？',
});

// 信息提示
await Modal.info({
  title: '操作成功',
  content: '数据已保存',
});
```

### 预设弹窗

```tsx
// 删除确认
await Modal.deleteConfirm({
  title: '删除用户',
  content: '确定要删除该用户吗？删除后无法恢复。',
  onConfirm: async () => {
    await deleteUser();
  },
});

// 警告弹窗
await Modal.warning({
  title: '警告',
  content: '此操作可能会影响系统性能',
  onConfirm: async () => {
    await performAction();
  },
});

// 成功提示
await Modal.success({
  title: '保存成功',
  content: '数据已成功保存到服务器',
});

// 错误提示
await Modal.error({
  title: '操作失败',
  content: '网络连接异常，请重试',
});
```

## 📚 API 文档

### Modal 对象

Modal 对象提供了所有弹窗方法的统一入口：

```tsx
Modal.form()          // 表单弹窗
Modal.confirm()       // 确认弹窗
Modal.info()          // 信息提示
Modal.success()       // 成功提示
Modal.warning()       // 警告弹窗
Modal.error()         // 错误提示
Modal.deleteConfirm() // 删除确认
```

### 表单弹窗 (FormModal)

```tsx
interface FormModalConfig<T> {
  title?: string;                    // 弹窗标题
  width?: number | string;           // 弹窗宽度
  children?: ReactNode;              // 弹窗内容
  defaultValues?: T;                 // 表单默认值
  formOptions?: UseFormProps<T>;     // react-hook-form 配置
  onOk?: SubmitHandler<T>;          // 确定按钮回调
  onAsyncOk?: (data: T) => Promise<void>; // 异步确定回调
  onCancel?: () => void;            // 取消按钮回调
  // ... 更多配置选项
}
```

### 确认弹窗 (ConfirmModal)

```tsx
interface ConfirmModalConfig {
  title?: string;                    // 弹窗标题
  content?: ReactNode;               // 确认内容
  type?: 'info' | 'success' | 'warning' | 'error'; // 确认类型
  onOk?: () => void;                // 确定按钮回调
  onAsyncOk?: () => Promise<void>;  // 异步确定回调
  onCancel?: () => void;            // 取消按钮回调
  // ... 更多配置选项
}
```

### 通用配置

```tsx
interface BaseModalConfig {
  title?: string;                    // 弹窗标题
  width?: number | string;           // 弹窗宽度 (默认: 520)
  loading?: boolean;                 // 是否显示加载状态
  disabled?: boolean;                // 是否禁用确定按钮
  okText?: string;                   // 确定按钮文本 (默认: '确定')
  cancelText?: string;               // 取消按钮文本 (默认: '取消')
  okType?: ButtonType;               // 确定按钮类型 (默认: 'primary')
  cancelType?: ButtonType;           // 取消按钮类型 (默认: 'normal')
  hideCancel?: boolean;              // 是否隐藏取消按钮
  noFooter?: boolean;                // 是否隐藏底部按钮区域
  maskClosable?: boolean;            // 点击遮罩是否关闭 (默认: false)
  destroyOnClose?: boolean;          // 关闭时是否销毁内容 (默认: true)
  bordered?: boolean;                // 是否显示边框 (默认: true)
}
```

## 🎯 高级用法

### 表单验证

```tsx
import { Controller, useFormContext } from 'react-hook-form';
import { Input } from '@pitrix/portal-ui';

const FormContent = () => {
  const { control, formState: { errors } } = useFormContext();
  
  return (
    <Controller
      name="email"
      control={control}
      rules={{
        required: '请输入邮箱',
        pattern: {
          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
          message: '请输入有效的邮箱地址',
        },
      }}
      render={({ field }) => (
        <div>
          <Input {...field} placeholder="请输入邮箱" />
          {errors.email && (
            <div style={{ color: 'red' }}>
              {errors.email.message}
            </div>
          )}
        </div>
      )}
    />
  );
};
```

### 异步操作

```tsx
await Modal.form({
  title: '保存数据',
  onAsyncOk: async (data) => {
    // 显示 loading 状态
    await saveToServer(data);
    // 自动隐藏 loading 并关闭弹窗
  },
});
```

### 错误处理

```tsx
try {
  const result = await Modal.form({
    title: '用户信息',
    onAsyncOk: async (data) => {
      await submitData(data);
    },
  });
  console.log('提交成功:', result);
} catch (error) {
  if (error.message === 'User cancelled') {
    console.log('用户取消了操作');
  } else {
    console.log('提交失败:', error);
  }
}
```

## 🎨 样式定制

### 尺寸预设

```tsx
import { SIZE_PRESETS } from 'components/Modal';

Modal.form({
  width: SIZE_PRESETS.large, // 720px
  // ...
});
```

### 自定义样式

```tsx
Modal.form({
  width: 800,
  bordered: false,
  // ...
});
```

## 🧪 测试

```tsx
import { TestApp } from 'components/Modal/test';

// 在您的应用中渲染 TestApp 组件进行测试
<TestApp />
```

## 📝 注意事项

1. **Provider 包装**: 确保在应用根组件包含 `NiceModal.Provider`
2. **表单上下文**: 表单内容组件需要使用 `useFormContext` 获取表单实例
3. **异步操作**: 异步操作会自动处理 loading 状态
4. **Promise 处理**: Promise reject 表示用户取消或操作失败
5. **类型安全**: 使用 TypeScript 泛型确保表单数据类型安全

## 🔗 相关链接

- [@ebay/nice-modal-react](https://github.com/eBay/nice-modal-react)
- [@pitrix/portal-ui](https://portal-ui.pitrix.io/)
- [react-hook-form](https://react-hook-form.com/)

## 📄 许可证

MIT
