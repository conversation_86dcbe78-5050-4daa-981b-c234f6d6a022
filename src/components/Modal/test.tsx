import React from 'react';
import NiceModal from '@ebay/nice-modal-react';
import { Button } from '@pitrix/portal-ui';
import Modal from './index';

// 简单的测试组件
export const ModalTest = () => {
  const handleTestForm = async () => {
    try {
      const result = await Modal.form<{ name: string }>({
        title: '测试表单',
        width: 400,
        defaultValues: { name: 'Test User' },
        children: (
          <div style={{ padding: '20px 0' }}>
            <p>这是一个测试表单弹窗</p>
            <p>表单数据将在控制台输出</p>
          </div>
        ),
        onAsyncOk: async (data) => {
          // eslint-disable-next-line no-console
          console.log('表单数据:', data);
        },
      });
      
      // eslint-disable-next-line no-console
      console.log('表单结果:', result);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('用户取消或出错:', error);
    }
  };

  const handleTestConfirm = async () => {
    try {
      await Modal.confirm({
        title: '测试确认',
        content: '这是一个测试确认弹窗，点击确定继续。',
        onAsyncOk: async () => {
          // eslint-disable-next-line no-console
          console.log('用户确认了操作');
        },
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('用户取消了操作');
    }
  };

  const handleTestInfo = async () => {
    await Modal.info({
      title: '测试信息',
      content: '这是一个信息提示弹窗。',
    });
  };

  const handleTestWarning = async () => {
    try {
      await Modal.warning({
        title: '测试警告',
        content: '这是一个警告弹窗，确定要继续吗？',
        onConfirm: async () => {
          // eslint-disable-next-line no-console
          console.log('用户确认了警告');
        },
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('用户取消了警告');
    }
  };

  const handleTestDelete = async () => {
    try {
      await Modal.deleteConfirm({
        title: '测试删除',
        content: '确定要删除这个测试项目吗？',
        onConfirm: async () => {
          // eslint-disable-next-line no-console
          console.log('用户确认了删除');
        },
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('用户取消了删除');
    }
  };

  const handleTestSuccess = async () => {
    await Modal.success({
      title: '测试成功',
      content: '操作已成功完成！',
    });
  };

  const handleTestError = async () => {
    await Modal.error({
      title: '测试错误',
      content: '发生了一个错误，请重试。',
    });
  };

  return (
    <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <h3>Modal 组件测试</h3>
      
      <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
        <Button onClick={handleTestForm}>
          测试表单弹窗
        </Button>
        <Button onClick={handleTestConfirm}>
          测试确认弹窗
        </Button>
        <Button onClick={handleTestInfo}>
          测试信息弹窗
        </Button>
        <Button onClick={handleTestWarning}>
          测试警告弹窗
        </Button>
        <Button onClick={handleTestDelete}>
          测试删除弹窗
        </Button>
        <Button onClick={handleTestSuccess}>
          测试成功弹窗
        </Button>
        <Button onClick={handleTestError}>
          测试错误弹窗
        </Button>
      </div>
    </div>
  );
};

// 测试应用组件，需要包装在 NiceModal.Provider 中
export const TestApp = () => {
  return (
    <NiceModal.Provider>
      <div>
        <h1>Modal 组件测试应用</h1>
        <ModalTest />
      </div>
    </NiceModal.Provider>
  );
};

export default TestApp;
