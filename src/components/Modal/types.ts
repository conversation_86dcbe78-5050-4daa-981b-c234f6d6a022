import { ReactNode } from 'react';
import { UseFormProps, FieldValues, SubmitHandler } from 'react-hook-form';

// 按钮类型定义
export type ButtonType = 'primary' | 'secondary' | 'normal' | 'danger';

// 确认弹窗类型
export type ConfirmType = 'info' | 'success' | 'warning' | 'error';

// 基础 Modal 配置
export interface BaseModalConfig {
  /** 弹窗标题 */
  title?: string;
  /** 弹窗宽度 */
  width?: number | string;
  /** 弹窗内容 */
  children?: ReactNode;
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 是否禁用确定按钮 */
  disabled?: boolean;
  /** 确定按钮文本 */
  okText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确定按钮类型 */
  okType?: ButtonType;
  /** 取消按钮类型 */
  cancelType?: ButtonType;
  /** 是否隐藏取消按钮 */
  hideCancel?: boolean;
  /** 是否隐藏底部按钮区域 */
  noFooter?: boolean;
  /** 点击遮罩是否关闭 */
  maskClosable?: boolean;
  /** 关闭时是否销毁内容 */
  destroyOnClose?: boolean;
  /** 是否显示边框 */
  bordered?: boolean;
}

// 表单 Modal 配置
export interface FormModalConfig<T extends FieldValues = FieldValues> extends BaseModalConfig {
  /** 表单默认值 */
  defaultValues?: T;
  /** react-hook-form 配置 */
  formOptions?: UseFormProps<T>;
  /** 确定按钮回调 */
  onOk?: SubmitHandler<T>;
  /** 异步确定按钮回调 */
  onAsyncOk?: (data: T) => Promise<void>;
  /** 取消按钮回调 */
  onCancel?: () => void;
}

// 确认 Modal 配置
export interface ConfirmModalConfig extends Omit<BaseModalConfig, 'formOptions' | 'defaultValues'> {
  /** 确认类型 */
  type?: ConfirmType;
  /** 确认内容 */
  content?: ReactNode;
  /** 确定按钮回调 */
  onOk?: () => void;
  /** 异步确定按钮回调 */
  onAsyncOk?: () => Promise<void>;
  /** 取消按钮回调 */
  onCancel?: () => void;
}

// Modal 实例方法
export interface ModalInstance {
  /** 显示弹窗 */
  show: () => void;
  /** 隐藏弹窗 */
  hide: () => void;
  /** 销毁弹窗 */
  destroy: () => void;
  /** 更新配置 */
  update: (config: Partial<BaseModalConfig>) => void;
}

// Modal 静态方法返回类型
export interface ModalReturn extends Promise<any> {
  /** Modal 实例 */
  modal: ModalInstance;
}

// 预设弹窗配置
export interface PresetModalConfig {
  title?: string;
  content?: ReactNode;
  onConfirm?: () => Promise<void>;
  onCancel?: () => void;
}

// 表单字段配置
export interface FormFieldConfig {
  name: string;
  label?: string;
  type?: 'input' | 'textarea' | 'select' | 'checkbox' | 'radio';
  placeholder?: string;
  required?: boolean;
  rules?: any;
  options?: Array<{ label: string; value: any }>;
}

// 快速表单配置
export interface QuickFormConfig<T extends FieldValues = FieldValues> extends Omit<FormModalConfig<T>, 'children'> {
  /** 表单字段配置 */
  fields: FormFieldConfig[];
}


