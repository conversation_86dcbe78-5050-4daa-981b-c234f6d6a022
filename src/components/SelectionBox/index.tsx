import React from 'react';
import { portal, Icon, Option, Css } from '@pitrix/portal-ui';
import { t } from '@pitrix/portal-widget';

interface SelectionBoxProps {
  /** 主要文本 */
  text: string;
  /** 单位文本 */
  unit?: string;
  // 展示的信息
  options?: Option[];
  onItemDelete?: (option: Option) => void;
  css?: Css;
}

const SelectionBox: React.FC<SelectionBoxProps> = ({ text, unit = t('项', 'items'), options, onItemDelete, css }) => {
  return (
    <portal.div
      css={{
        alignItems: 'center',
        color: '#666',
        backgroundColor: 'fill.deep',
        borderRadius: '2px',
        margin: '16px 0 0 0',
        ...css,
      }}
    >
      <portal.div
        css={{
          display: 'flex',
          alignItems: 'center',
          fontWeight: '500',
          lineHeight: '20px',
          color: 'text.base',
          padding: '12px 12px 8px',
        }}
      >
        <portal.span>{text}</portal.span>
        <portal.span css={{ color: 'brand.default', padding: '0 4px' }}>{options?.length}</portal.span>
        <portal.span>{unit}</portal.span>
      </portal.div>
      <portal.div
        css={{
          display: 'flex',
          paddingLeft: 12,
          minHeight: 36,
          maxHeight: 46,
          overflowY: 'auto',
          flexWrap: 'wrap',
          gap: '0 8px',
        }}
      >
        {options?.map((option: Option) => (
          <portal.div
            key={option.value}
            css={{
              lineHeight: '24px',
              backgroundColor: '#fff',
              borderRadius: '2px',
              padding: '0 8px',
              display: 'flex',
              height: 24,
              gap: 4,
              alignItems: 'center',
              marginBottom: 8,
            }}
          >
            <portal.span
              title={option.label}
              css={{
                color: 'text.base',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: 120,
              }}
            >
              {option.label}
            </portal.span>
            <portal.div css={{ height: 12, width: 1, backgroundColor: 'text.base' }} />
            <portal.span css={{ color: 'text.placeholder' }}>{option.value}</portal.span>
            <portal.span css={{ display: 'flex', cursor: 'pointer' }} onClick={() => onItemDelete?.(option)}>
              <Icon name="close_fill" size={14} />
            </portal.span>
          </portal.div>
        ))}
      </portal.div>
    </portal.div>
  );
};

export default SelectionBox;
