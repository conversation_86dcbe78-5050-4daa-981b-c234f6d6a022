import React from 'react';
import { Input, But<PERSON>, Link, portal } from '@pitrix/portal-ui';

interface Props {
  placeholder: string;
  refresh: () => void;
  onSearch: (word: string) => void;
  link?: {
    href: string;
    title: string;
  };
}

function TableToolbar({ placeholder, refresh, onSearch, link }: Props): JSX.Element {
  return (
    <portal.div css={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
      <portal.div
        css={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 8,
          marginBottom: 12,
        }}
      >
        <Input
          clearable
          width={272}
          prefix="MagnifierFill"
          placeholder={placeholder}
          onPressEnter={(e) => {
            const v = (e.target as HTMLInputElement)?.value;
            onSearch(v);
          }}
          onClear={() => {
            onSearch('');
          }}
        />
        <Button icon="Refresh2Fill" onClick={() => refresh()} />
        {/* <Checkbox label="仅查看可用" value="" /> */}
      </portal.div>
      {link ? (
        <Link href={link?.href} iconName="ShareBoxFill" target="_blank" iconPlacement="after">
          {link?.title ?? ''}
        </Link>
      ) : null}
    </portal.div>
  );
}

export default TableToolbar;
