import React, { useState, useRef, useEffect } from 'react';
import { theme, Icon } from '@pitrix/portal-ui';
import styled from '@emotion/styled';
import { t } from '@pitrix/portal-widget';
import { css as emotionCss, CSSObject, SerializedStyles } from '@emotion/react';

interface FormFieldItem {
  resource_id: string;
  resource_name: string;
  link?: string;
}

// 展示类型
type DisplayType = 'default' | 'vertical' | 'horizontal';

interface ExpandableListProps {
  /** 表单标题 */
  title: string;
  /** 展示类型 */
  displayType?: DisplayType;
  /** 自定义内容 (当 displayType 为 'default' 时使用) */
  children?: React.ReactNode;
  /** 数据列表 (当 displayType 为 'vertical' 或 'horizontal' 时使用) */
  data?: FormFieldItem[];
  /** 左侧Title的行高，默认32px */
  titleLineHeight?: string | number;
  /** 最大显示数量，超过此数量会显示展开按钮 (仅对 vertical 模式有效) */
  maxVisibleCount?: number;
  /** 自定义样式 */
  style?: React.CSSProperties | CSSObject;
}

type StyledBoxProps = {
  css?: SerializedStyles;
};

const Container = styled.div<StyledBoxProps>`
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 24px;
  font-size: 12px;
  ${(props: StyledBoxProps) => props.css}
`;

const Title = styled.div<{ titleLineHeight?: string | number }>`
  width: 96px;
  flex-shrink: 0;
  font-size: 12px;
  color: ${theme.colors.text.base};
  line-height: ${(props: { titleLineHeight: string | number }) => props.titleLineHeight || '32px'};
  font-weight: 500;
`;

const Content = styled.div`
  flex: 1;
  min-width: 0;
`;

// 自定义内容容器
const CustomContent = styled.div`
  color: ${theme.colors.text.base};
  line-height: 20px;
`;

// 垂直布局样式
const VerticalList = styled.div`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const VerticalListItem = styled.div`
  color: ${theme.colors.text.base};
  display: flex;
  align-items: center;
  gap: 4px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const VerticalItemName = styled.span`
  max-width: calc(100% - 64px);
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const VerticalItemID = styled.span`
  color: ${theme.colors.text.placeholder};
`;

// 水平布局样式
const HorizontalContainer = styled.div<{ expanded: boolean }>`
  display: flex;
  flex-wrap: wrap;
  max-height: ${(props: { expanded: boolean }) => (props.expanded ? 'none' : '40px')};
  overflow: hidden;
  transition: max-height 0.3s ease;
`;

const HorizontalItem = styled.div`
  line-height: 20px;
  display: flex;
  align-items: center;
`;

const HorizontalItemName = styled.a`
  max-width: 120px;
  color: ${theme.colors.link.default};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  &:hover {
    color: ${theme.colors.link.hover};
  }
`;

const ExpandButton = styled.div`
  color: ${theme.colors.brand.default};
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  line-height: 20px;

  &:hover {
    color: ${theme.colors.brand.hover};
  }

  &:focus {
    outline: none;
  }
`;

export const FormField: React.FC<ExpandableListProps> = ({
  title,
  displayType = 'default',
  children,
  data = [],
  maxVisibleCount = 2,
  titleLineHeight,
  style,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [needsExpansion, setNeedsExpansion] = useState(false);
  const horizontalContainerRef = useRef<HTMLDivElement>(null);

  // 检查水平布局是否需要展开按钮
  useEffect(() => {
    if (displayType === 'horizontal' && horizontalContainerRef.current) {
      const container = horizontalContainerRef.current;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      setNeedsExpansion(scrollHeight > clientHeight);
    }
  }, [data, displayType]);

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  // 自定义内容渲染
  const renderCustomContent = () => {
    return <CustomContent>{children}</CustomContent>;
  };

  // 垂直布局渲染
  const renderVerticalLayout = () => {
    if (!data || data.length === 0) {
      return null;
    }

    const visibleData = expanded ? data : data.slice(0, maxVisibleCount);
    const hasMoreItems = data.length > maxVisibleCount;
    const remainingCount = data.length - maxVisibleCount;

    return (
      <>
        <VerticalList>
          {visibleData.map((item) => (
            <VerticalListItem key={item.resource_id}>
              <VerticalItemName title={item.resource_name}>{item.resource_name || '––'}</VerticalItemName>
              <span>|</span>
              <VerticalItemID>{item.resource_id}</VerticalItemID>
            </VerticalListItem>
          ))}
        </VerticalList>

        {hasMoreItems && (
          <ExpandButton onClick={handleToggle}>
            {expanded ? t('收起', 'Collapse') : `${t('展开全部', 'Expand All')} (${remainingCount})`}
            <Icon size={14} name={expanded ? 'chevron_up_duotone' : 'chevron_down_duotone'} />
          </ExpandButton>
        )}
      </>
    );
  };

  // 水平布局渲染
  const renderHorizontalLayout = () => {
    if (!data || data.length === 0) {
      return null;
    }

    return (
      <>
        <HorizontalContainer ref={horizontalContainerRef} expanded={expanded}>
          {data.map((item, index) => (
            <HorizontalItem key={item.resource_id} title={`${item.resource_name} | ${item.resource_id}`}>
              <HorizontalItemName href={item?.link}>{item.resource_name || '––'}</HorizontalItemName>
              {index === data.length - 1 ? '' : '、'}
            </HorizontalItem>
          ))}
        </HorizontalContainer>

        {needsExpansion && (
          <ExpandButton onClick={handleToggle}>
            {expanded ? t('收起', 'Collapse') : t('展开全部', 'Expand All')}
            <Icon size={14} name={expanded ? 'chevron_up_duotone' : 'chevron_down_duotone'} />
          </ExpandButton>
        )}
      </>
    );
  };

  // 根据类型渲染内容
  const renderContent = () => {
    switch (displayType) {
      case 'horizontal':
        return renderHorizontalLayout();
      case 'default':
        return renderCustomContent();
      case 'vertical':
        return renderVerticalLayout();
      default:
        return renderCustomContent();
    }
  };
  return (
    <Container css={style ? emotionCss(style as CSSObject) : undefined}>
      <Title titleLineHeight={titleLineHeight}>{title}</Title>
      <Content>{renderContent()}</Content>
    </Container>
  );
};

export default FormField;
