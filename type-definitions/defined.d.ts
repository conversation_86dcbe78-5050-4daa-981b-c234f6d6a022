declare module '*.js';

declare module '@pitrix/portal-widget' {
  // Translation function
  export function t(key: string, defaultValue?: string, params?: object): string;

  // Re-export common types and interfaces that are used in the codebase
  export interface ConfigApiData {
    global_config: any;
    all_zone_config: {
      default: any;
      [key: string]: any;
    };
    theme_config: any;
    settings: any;
    products_brief: any;
  }

  export interface UserData {
    [key: string]: any;
  }

  export interface Brand {
    [key: string]: any;
  }

  export interface ThemeConfig {
    [key: string]: any;
  }

  export interface Image {
    [key: string]: any;
  }

  export interface DescribeImagesRequest {
    [key: string]: any;
  }

  export interface UploadUserDataAttachmentResponse {
    [key: string]: any;
  }

  // React Query hooks
  export function useDescribeImagesQuery(params: DescribeImagesRequest): any;

  // Provider component
  export const PortalProvider: React.ComponentType<any>;

  // Other commonly used exports
  export const useAttachImage: any;
  export const BaseResponse: any;
}
